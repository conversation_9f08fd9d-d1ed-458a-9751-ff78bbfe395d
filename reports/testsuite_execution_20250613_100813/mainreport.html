<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/13/2025, 10:08:45 AM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">0</span> passed,
                <span class="failed-count">3</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 13/06/2025, 10:08:45
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #1 health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="failed"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="Successful.png" data-action-id="Successful" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Add Log: Launched App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: Successful">Successful</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Add Log: Edit link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Add Log: Done link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Wait for 1 ms <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #2 apple health (Copy)
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            7 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="failed"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="failed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="failed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="failed"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="failed"
                            data-screenshot="Successful.png" data-action-id="Successful" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Add Log: Closed App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: Successful">Successful</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="failed"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Execute Test Case: health2 (9 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery) <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #3 apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="failed"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="successful.png" data-action-id="successful" onclick="showStepDetails('step-2-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Add Log: Clicked on Edit link successfully (with screenshot) <span class="action-id-badge" title="Action ID: successful">successful</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-2-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: apple health (8 steps) <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-2-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 13/06/2025, 10:08:45","testCases":[{"name":"health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"failed","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Add Log: Launched App Successfully (with screenshot)","status":"unknown","duration":"0ms","action_id":"Successful","screenshot_filename":"Successful.png","report_screenshot":"Successful.png","resolved_screenshot":"screenshots/Successful.png","action_id_screenshot":"screenshots/Successful.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Edit link is clicked (with screenshot)","status":"unknown","duration":"0ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Done link is clicked (with screenshot)","status":"unknown","duration":"0ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"},{"name":"Wait for 1 ms","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Terminate app: com.apple.Health","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Add Log: App is closed (with screenshot)","status":"unknown","duration":"0ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"}]},{"name":"apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"failed","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]","status":"failed","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"failed","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"failed","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Add Log: Closed App Successfully (with screenshot)","status":"failed","duration":"0ms","action_id":"Successful","screenshot_filename":"Successful.png","report_screenshot":"Successful.png","resolved_screenshot":"screenshots/Successful.png","action_id_screenshot":"screenshots/Successful.png"},{"name":"Execute Test Case: health2 (9 steps)","status":"failed","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"}]},{"name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"failed","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Clicked on Edit link successfully (with screenshot)","status":"unknown","duration":"0ms","action_id":"successful","screenshot_filename":"successful.png","report_screenshot":"successful.png","resolved_screenshot":"screenshots/successful.png","action_id_screenshot":"screenshots/successful.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Execute Test Case: apple health (8 steps)","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Add Log: App is closed (with screenshot)","status":"unknown","duration":"0ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"}]}],"passed":0,"failed":3,"skipped":0,"status":"failed","availableScreenshots":[],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>