Action Log - 2025-06-13 10:08:45
================================================================================

[[10:08:45]] [INFO] Generating execution report...
[[10:08:45]] [INFO] Skipping remaining steps in failed test case (moving from action 17 to next test case at 25)
[[10:08:45]] [ERROR] Action 17 failed: No device connected
[[10:08:35]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[10:08:35]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[10:08:35]] [INFO] Moving to the next test case after failure (server will handle retry)
[[10:08:35]] [ERROR] Multi Step action step 1 failed: No device connected
[[10:08:33]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[10:08:33]] [INFO] Loaded 9 steps from test case: health2
[[10:08:33]] [INFO] Loading steps for Multi Step action: health2
[[10:08:33]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[10:08:33]] [ERROR] All Hook Actions failed: No device connected
[[10:08:31]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:08:31]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:08:31]] [ERROR] Action 14 failed: No device connected
[[10:08:31]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[10:08:31]] [ERROR] All Hook Actions failed: No device connected
[[10:08:29]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:08:29]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:08:29]] [ERROR] Action 13 failed: No device connected
[[10:08:29]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[10:08:29]] [ERROR] All Hook Actions failed: No device connected
[[10:08:27]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:08:27]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:08:27]] [ERROR] Action 12 failed: No device connected
[[10:08:27]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:08:27]] [ERROR] All Hook Actions failed: No device connected
[[10:08:25]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:08:25]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:08:25]] [ERROR] Action 11 failed: No device connected
[[10:08:25]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[10:08:25]] [ERROR] All Hook Actions failed: No device connected
[[10:08:23]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:08:23]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:08:23]] [ERROR] Action 10 failed: No device connected
[[10:08:13]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[10:08:13]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 9)
[[10:08:13]] [ERROR] Action 1 failed: No device connected
[[10:08:13]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[10:08:13]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[10:08:13]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[10:08:13]] [INFO] Clearing screenshots from database before execution...
[[10:08:13]] [SUCCESS] All screenshots deleted successfully
[[10:08:13]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:08:13]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_100813/screenshots
[[10:08:13]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_100813
[[10:08:13]] [SUCCESS] Report directory initialized successfully
[[10:08:13]] [INFO] Initializing report directory and screenshots folder...
[[10:08:11]] [SUCCESS] All screenshots deleted successfully
[[10:08:11]] [INFO] All actions cleared
[[10:08:11]] [INFO] Cleaning up screenshots...
[[10:08:06]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[10:08:06]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[10:08:00]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[10:07:55]] [SUCCESS] Found 1 device(s)
[[10:07:55]] [INFO] Refreshing device list...
