Action Log - 2025-06-13 09:44:53
================================================================================

[[09:44:53]] [INFO] Generating execution report...
[[09:44:53]] [INFO] Skipping remaining steps in failed test case (moving from action 17 to next test case at 25)
[[09:44:53]] [ERROR] Action 17 failed: No device connected
[[09:44:43]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[09:44:43]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[09:44:43]] [INFO] Moving to the next test case after failure (server will handle retry)
[[09:44:43]] [ERROR] Multi Step action step 1 failed: No device connected
[[09:44:41]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[09:44:41]] [INFO] Loaded 9 steps from test case: health2
[[09:44:41]] [INFO] Loading steps for Multi Step action: health2
[[09:44:41]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[09:44:41]] [ERROR] All Hook Actions failed: No device connected
[[09:44:39]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[09:44:39]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[09:44:39]] [ERROR] Action 14 failed: No device connected
[[09:44:39]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[09:44:39]] [ERROR] All Hook Actions failed: No device connected
[[09:44:37]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[09:44:37]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[09:44:37]] [ERROR] Action 13 failed: No device connected
[[09:44:37]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[09:44:37]] [ERROR] All Hook Actions failed: No device connected
[[09:44:35]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[09:44:35]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[09:44:35]] [ERROR] Action 12 failed: No device connected
[[09:44:35]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[09:44:35]] [ERROR] All Hook Actions failed: No device connected
[[09:44:33]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[09:44:33]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[09:44:33]] [ERROR] Action 11 failed: No device connected
[[09:44:33]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[09:44:33]] [ERROR] All Hook Actions failed: No device connected
[[09:44:31]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[09:44:31]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[09:44:31]] [ERROR] Action 10 failed: No device connected
[[09:44:21]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[09:44:21]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 9)
[[09:44:21]] [ERROR] Action 1 failed: No device connected
[[09:44:21]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[09:44:21]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[09:44:21]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[09:44:21]] [INFO] Clearing screenshots from database before execution...
[[09:44:21]] [SUCCESS] All screenshots deleted successfully
[[09:44:21]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[09:44:21]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_094421/screenshots
[[09:44:21]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_094421
[[09:44:21]] [SUCCESS] Report directory initialized successfully
[[09:44:21]] [INFO] Initializing report directory and screenshots folder...
[[09:44:18]] [SUCCESS] All screenshots deleted successfully
[[09:44:18]] [INFO] All actions cleared
[[09:44:18]] [INFO] Cleaning up screenshots...
[[09:44:00]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E
[[09:44:00]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[09:43:55]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[09:39:12]] [SUCCESS] Found 1 device(s)
[[09:39:11]] [INFO] Refreshing device list...
