Action Log - 2025-06-13 10:12:23
================================================================================

[[10:12:22]] [INFO] Generating execution report...
[[10:12:22]] [INFO] Skipping remaining steps in failed test case (moving from action 17 to next test case at 25)
[[10:12:22]] [ERROR] Action 17 failed: No device connected
[[10:12:12]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[10:12:12]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[10:12:12]] [INFO] Moving to the next test case after failure (server will handle retry)
[[10:12:12]] [ERROR] Multi Step action step 1 failed: No device connected
[[10:12:10]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[10:12:10]] [INFO] Loaded 9 steps from test case: health2
[[10:12:10]] [INFO] Loading steps for Multi Step action: health2
[[10:12:10]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[10:12:10]] [ERROR] All Hook Actions failed: No device connected
[[10:12:08]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:12:08]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:12:08]] [ERROR] Action 14 failed: No device connected
[[10:12:08]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[10:12:08]] [ERROR] All Hook Actions failed: No device connected
[[10:12:06]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:12:06]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:12:06]] [ERROR] Action 13 failed: No device connected
[[10:12:06]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[10:12:06]] [ERROR] All Hook Actions failed: No device connected
[[10:12:04]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:12:04]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:12:04]] [ERROR] Action 12 failed: No device connected
[[10:12:04]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:12:04]] [ERROR] All Hook Actions failed: No device connected
[[10:12:02]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:12:02]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:12:02]] [ERROR] Action 11 failed: No device connected
[[10:12:02]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[10:12:02]] [ERROR] All Hook Actions failed: No device connected
[[10:12:00]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:12:00]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:12:00]] [ERROR] Action 10 failed: No device connected
[[10:11:50]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[10:11:50]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 9)
[[10:11:50]] [ERROR] Action 1 failed: No device connected
[[10:11:50]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[10:11:50]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[10:11:50]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[10:11:50]] [INFO] Clearing screenshots from database before execution...
[[10:11:50]] [SUCCESS] All screenshots deleted successfully
[[10:11:50]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:11:50]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_101150/screenshots
[[10:11:50]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_101150
[[10:11:50]] [SUCCESS] Report directory initialized successfully
[[10:11:50]] [INFO] Initializing report directory and screenshots folder...
[[10:11:47]] [SUCCESS] All screenshots deleted successfully
[[10:11:47]] [INFO] All actions cleared
[[10:11:47]] [INFO] Cleaning up screenshots...
[[10:11:41]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[10:11:41]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[10:11:39]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[10:11:38]] [SUCCESS] Found 1 device(s)
[[10:11:37]] [INFO] Refreshing device list...
