Action Log - 2025-06-13 10:42:50
================================================================================

[[10:42:50]] [INFO] Generating execution report...
[[10:42:50]] [INFO] Skipping remaining steps in failed test case (moving from action 17 to next test case at 25)
[[10:42:50]] [ERROR] Action 17 failed: No device connected
[[10:42:40]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[10:42:40]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[10:42:40]] [INFO] Moving to the next test case after failure (server will handle retry)
[[10:42:40]] [ERROR] Multi Step action step 1 failed: No device connected
[[10:42:38]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[10:42:38]] [INFO] Loaded 9 steps from test case: health2
[[10:42:38]] [INFO] Loading steps for Multi Step action: health2
[[10:42:38]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[10:42:38]] [ERROR] All Hook Actions failed: No device connected
[[10:42:36]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:42:36]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:42:36]] [ERROR] Action 14 failed: No device connected
[[10:42:36]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[10:42:36]] [ERROR] All Hook Actions failed: No device connected
[[10:42:34]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:42:34]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:42:34]] [ERROR] Action 13 failed: No device connected
[[10:42:34]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[10:42:34]] [ERROR] All Hook Actions failed: No device connected
[[10:42:32]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:42:32]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:42:32]] [ERROR] Action 12 failed: No device connected
[[10:42:32]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:42:32]] [ERROR] All Hook Actions failed: No device connected
[[10:42:30]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:42:30]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:42:30]] [ERROR] Action 11 failed: No device connected
[[10:42:30]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[10:42:30]] [ERROR] All Hook Actions failed: No device connected
[[10:42:28]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:42:28]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:42:28]] [ERROR] Action 10 failed: No device connected
[[10:42:18]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[10:42:18]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 9)
[[10:42:18]] [ERROR] Action 1 failed: No device connected
[[10:42:18]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[10:42:18]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[10:42:18]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[10:42:18]] [INFO] Clearing screenshots from database before execution...
[[10:42:18]] [SUCCESS] All screenshots deleted successfully
[[10:42:18]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:42:18]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_104218/screenshots
[[10:42:18]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_104218
[[10:42:18]] [SUCCESS] Report directory initialized successfully
[[10:42:18]] [INFO] Initializing report directory and screenshots folder...
[[10:42:15]] [SUCCESS] All screenshots deleted successfully
[[10:42:15]] [INFO] All actions cleared
[[10:42:15]] [INFO] Cleaning up screenshots...
[[10:42:11]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[10:42:11]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[10:42:08]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[10:42:07]] [SUCCESS] Found 1 device(s)
[[10:42:06]] [INFO] Refreshing device list...
