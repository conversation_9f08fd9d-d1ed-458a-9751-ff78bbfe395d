Action Log - 2025-06-13 10:06:33
================================================================================

[[10:06:33]] [INFO] Generating execution report...
[[10:06:33]] [INFO] Skipping remaining steps in failed test case (moving from action 17 to next test case at 25)
[[10:06:33]] [ERROR] Action 17 failed: No device connected
[[10:06:22]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[10:06:22]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[10:06:22]] [INFO] Moving to the next test case after failure (server will handle retry)
[[10:06:22]] [ERROR] Multi Step action step 1 failed: No device connected
[[10:06:20]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[10:06:20]] [INFO] Loaded 9 steps from test case: health2
[[10:06:20]] [INFO] Loading steps for Multi Step action: health2
[[10:06:20]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[10:06:20]] [ERROR] All Hook Actions failed: No device connected
[[10:06:18]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:06:18]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:06:18]] [ERROR] Action 14 failed: No device connected
[[10:06:18]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[10:06:18]] [ERROR] All Hook Actions failed: No device connected
[[10:06:16]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:06:16]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:06:16]] [ERROR] Action 13 failed: No device connected
[[10:06:16]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[10:06:16]] [ERROR] All Hook Actions failed: No device connected
[[10:06:14]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:06:14]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:06:14]] [ERROR] Action 12 failed: No device connected
[[10:06:14]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:06:14]] [ERROR] All Hook Actions failed: No device connected
[[10:06:12]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:06:12]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:06:12]] [ERROR] Action 11 failed: No device connected
[[10:06:11]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[10:06:11]] [ERROR] All Hook Actions failed: No device connected
[[10:06:09]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:06:09]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:06:09]] [ERROR] Action 10 failed: No device connected
[[10:05:59]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[10:05:59]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 9)
[[10:05:59]] [ERROR] Action 1 failed: No device connected
[[10:05:59]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[10:05:59]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[10:05:59]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[10:05:59]] [INFO] Clearing screenshots from database before execution...
[[10:05:59]] [SUCCESS] All screenshots deleted successfully
[[10:05:59]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:05:59]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_100559/screenshots
[[10:05:59]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_100559
[[10:05:59]] [SUCCESS] Report directory initialized successfully
[[10:05:59]] [INFO] Initializing report directory and screenshots folder...
[[10:05:58]] [SUCCESS] All screenshots deleted successfully
[[10:05:58]] [INFO] All actions cleared
[[10:05:58]] [INFO] Cleaning up screenshots...
[[10:05:55]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[10:05:55]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[10:05:49]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[10:05:45]] [SUCCESS] Found 1 device(s)
[[10:05:45]] [INFO] Refreshing device list...
