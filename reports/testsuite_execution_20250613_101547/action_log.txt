Action Log - 2025-06-13 10:16:19
================================================================================

[[10:16:19]] [INFO] Generating execution report...
[[10:16:19]] [INFO] Skipping remaining steps in failed test case (moving from action 17 to next test case at 25)
[[10:16:19]] [ERROR] Action 17 failed: No device connected
[[10:16:09]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[10:16:09]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[10:16:09]] [INFO] Moving to the next test case after failure (server will handle retry)
[[10:16:09]] [ERROR] Multi Step action step 1 failed: No device connected
[[10:16:07]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[10:16:07]] [INFO] Loaded 9 steps from test case: health2
[[10:16:07]] [INFO] Loading steps for Multi Step action: health2
[[10:16:07]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[10:16:07]] [ERROR] All Hook Actions failed: No device connected
[[10:16:05]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:16:05]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:16:05]] [ERROR] Action 14 failed: No device connected
[[10:16:05]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[10:16:05]] [ERROR] All Hook Actions failed: No device connected
[[10:16:03]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:16:03]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:16:03]] [ERROR] Action 13 failed: No device connected
[[10:16:03]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[10:16:03]] [ERROR] All Hook Actions failed: No device connected
[[10:16:01]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:16:01]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:16:01]] [ERROR] Action 12 failed: No device connected
[[10:16:01]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:16:01]] [ERROR] All Hook Actions failed: No device connected
[[10:15:59]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:15:59]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:15:59]] [ERROR] Action 11 failed: No device connected
[[10:15:59]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[10:15:59]] [ERROR] All Hook Actions failed: No device connected
[[10:15:57]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:15:57]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:15:57]] [ERROR] Action 10 failed: No device connected
[[10:15:47]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[10:15:47]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 9)
[[10:15:47]] [ERROR] Action 1 failed: No device connected
[[10:15:47]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[10:15:47]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[10:15:47]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[10:15:47]] [INFO] Clearing screenshots from database before execution...
[[10:15:47]] [SUCCESS] All screenshots deleted successfully
[[10:15:47]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:15:47]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_101547/screenshots
[[10:15:47]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_101547
[[10:15:47]] [SUCCESS] Report directory initialized successfully
[[10:15:47]] [INFO] Initializing report directory and screenshots folder...
[[10:15:45]] [SUCCESS] All screenshots deleted successfully
[[10:15:45]] [INFO] All actions cleared
[[10:15:45]] [INFO] Cleaning up screenshots...
[[10:15:39]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[10:15:39]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[10:15:33]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[10:15:32]] [SUCCESS] Found 1 device(s)
[[10:15:31]] [INFO] Refreshing device list...
