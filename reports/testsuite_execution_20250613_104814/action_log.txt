Action Log - 2025-06-13 10:48:47
================================================================================

[[10:48:46]] [INFO] Generating execution report...
[[10:48:46]] [INFO] Skipping remaining steps in failed test case (moving from action 17 to next test case at 25)
[[10:48:46]] [ERROR] Action 17 failed: name '_VARS' is not defined
[[10:48:36]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[10:48:36]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[10:48:36]] [INFO] Moving to the next test case after failure (server will handle retry)
[[10:48:36]] [ERROR] Multi Step action step 1 failed: name '_VARS' is not defined
[[10:48:34]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[10:48:34]] [INFO] Loaded 9 steps from test case: health2
[[10:48:34]] [INFO] Loading steps for Multi Step action: health2
[[10:48:34]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[10:48:34]] [ERROR] All Hook Actions failed: No module named 'app.app'; 'app' is not a package
[[10:48:32]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:48:32]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:48:32]] [ERROR] Action 14 failed: name '_VARS' is not defined
[[10:48:32]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[10:48:32]] [ERROR] All Hook Actions failed: No module named 'app.app'; 'app' is not a package
[[10:48:30]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:48:30]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:48:30]] [ERROR] Action 13 failed: name '_VARS' is not defined
[[10:48:30]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[10:48:30]] [ERROR] All Hook Actions failed: No module named 'app.app'; 'app' is not a package
[[10:48:28]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:48:28]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:48:28]] [ERROR] Action 12 failed: name '_VARS' is not defined
[[10:48:28]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:48:28]] [ERROR] All Hook Actions failed: No module named 'app.app'; 'app' is not a package
[[10:48:26]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:48:26]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:48:26]] [ERROR] Action 11 failed: name '_VARS' is not defined
[[10:48:26]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[10:48:26]] [ERROR] All Hook Actions failed: No module named 'app.app'; 'app' is not a package
[[10:48:24]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:48:24]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:48:24]] [ERROR] Action 10 failed: name '_VARS' is not defined
[[10:48:14]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[10:48:14]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 9)
[[10:48:14]] [ERROR] Action 1 failed: name '_VARS' is not defined
[[10:48:14]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[10:48:14]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[10:48:14]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[10:48:14]] [INFO] Clearing screenshots from database before execution...
[[10:48:14]] [SUCCESS] All screenshots deleted successfully
[[10:48:14]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:48:14]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_104814/screenshots
[[10:48:14]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_104814
[[10:48:14]] [SUCCESS] Report directory initialized successfully
[[10:48:14]] [INFO] Initializing report directory and screenshots folder...
[[10:48:12]] [SUCCESS] All screenshots deleted successfully
[[10:48:12]] [INFO] All actions cleared
[[10:48:12]] [INFO] Cleaning up screenshots...
[[10:48:07]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[10:48:07]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[10:48:04]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[10:48:02]] [SUCCESS] Found 1 device(s)
[[10:48:01]] [INFO] Refreshing device list...
