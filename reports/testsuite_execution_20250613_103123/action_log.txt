Action Log - 2025-06-13 10:31:55
================================================================================

[[10:31:55]] [INFO] Generating execution report...
[[10:31:55]] [INFO] Skipping remaining steps in failed test case (moving from action 17 to next test case at 25)
[[10:31:55]] [ERROR] Action 17 failed: No device connected
[[10:31:45]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[10:31:45]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[10:31:45]] [INFO] Moving to the next test case after failure (server will handle retry)
[[10:31:45]] [ERROR] Multi Step action step 1 failed: No device connected
[[10:31:43]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[10:31:43]] [INFO] Loaded 9 steps from test case: health2
[[10:31:43]] [INFO] Loading steps for Multi Step action: health2
[[10:31:43]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[10:31:43]] [ERROR] All Hook Actions failed: No device connected
[[10:31:41]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:31:41]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:31:41]] [ERROR] Action 14 failed: No device connected
[[10:31:41]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[10:31:41]] [ERROR] All Hook Actions failed: No device connected
[[10:31:39]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:31:39]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:31:39]] [ERROR] Action 13 failed: No device connected
[[10:31:39]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[10:31:39]] [ERROR] All Hook Actions failed: No device connected
[[10:31:37]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:31:37]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:31:37]] [ERROR] Action 12 failed: No device connected
[[10:31:37]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:31:37]] [ERROR] All Hook Actions failed: No device connected
[[10:31:35]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:31:35]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:31:35]] [ERROR] Action 11 failed: No device connected
[[10:31:35]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[10:31:35]] [ERROR] All Hook Actions failed: No device connected
[[10:31:33]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[10:31:33]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[10:31:33]] [ERROR] Action 10 failed: No device connected
[[10:31:23]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[10:31:23]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 9)
[[10:31:23]] [ERROR] Action 1 failed: No device connected
[[10:31:23]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[10:31:23]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[10:31:23]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[10:31:23]] [INFO] Clearing screenshots from database before execution...
[[10:31:23]] [SUCCESS] All screenshots deleted successfully
[[10:31:23]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:31:23]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_103123/screenshots
[[10:31:23]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_103123
[[10:31:23]] [SUCCESS] Report directory initialized successfully
[[10:31:23]] [INFO] Initializing report directory and screenshots folder...
[[10:31:20]] [SUCCESS] All screenshots deleted successfully
[[10:31:20]] [INFO] All actions cleared
[[10:31:20]] [INFO] Cleaning up screenshots...
[[10:31:16]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[10:31:16]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[10:31:13]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[10:31:10]] [SUCCESS] Found 1 device(s)
[[10:31:09]] [INFO] Refreshing device list...
