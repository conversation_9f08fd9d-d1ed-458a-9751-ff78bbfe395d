Action Log - 2025-06-13 11:42:22
================================================================================

[[11:42:22]] [INFO] Generating execution report...
[[11:42:21]] [SUCCESS] Screenshot refreshed successfully
[[11:42:21]] [SUCCESS] Screenshot refreshed
[[11:42:20]] [INFO] Refreshing screenshot...
[[11:42:17]] [SUCCESS] Screenshot refreshed successfully
[[11:42:17]] [INFO] Executing action 25/25: Add Log: App is closed (with screenshot)
[[11:42:17]] [SUCCESS] Screenshot refreshed
[[11:42:16]] [INFO] Refreshing screenshot...
[[11:42:16]] [SUCCESS] Screenshot refreshed successfully
[[11:42:16]] [SUCCESS] Screenshot refreshed
[[11:42:14]] [INFO] Refreshing screenshot...
[[11:42:12]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[11:42:11]] [SUCCESS] Screenshot refreshed successfully
[[11:42:11]] [SUCCESS] Screenshot refreshed
[[11:42:10]] [INFO] Refreshing screenshot...
[[11:41:56]] [INFO] Executing Multi Step action step 8/9: Execute Test Case: apple health (8 steps)
[[11:41:55]] [SUCCESS] Screenshot refreshed successfully
[[11:41:55]] [SUCCESS] Screenshot refreshed
[[11:41:54]] [INFO] Refreshing screenshot...
[[11:41:50]] [INFO] Executing Multi Step action step 7/9: Terminate app: com.apple.Health
[[11:41:50]] [SUCCESS] Screenshot refreshed successfully
[[11:41:50]] [SUCCESS] Screenshot refreshed
[[11:41:49]] [INFO] Refreshing screenshot...
[[11:41:46]] [INFO] Executing Multi Step action step 6/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[11:41:45]] [SUCCESS] Screenshot refreshed successfully
[[11:41:45]] [SUCCESS] Screenshot refreshed
[[11:41:45]] [INFO] Refreshing screenshot...
[[11:41:43]] [INFO] Executing Multi Step action step 5/9: Add Log: Clicked on Edit link successfully (with screenshot)
[[11:41:43]] [SUCCESS] Screenshot refreshed successfully
[[11:41:43]] [SUCCESS] Screenshot refreshed
[[11:41:42]] [INFO] Refreshing screenshot...
[[11:41:40]] [INFO] Executing Multi Step action step 4/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[11:41:39]] [SUCCESS] Screenshot refreshed successfully
[[11:41:39]] [SUCCESS] Screenshot refreshed
[[11:41:38]] [INFO] Refreshing screenshot...
[[11:41:35]] [INFO] Executing Multi Step action step 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[11:41:35]] [SUCCESS] Screenshot refreshed successfully
[[11:41:35]] [SUCCESS] Screenshot refreshed
[[11:41:34]] [INFO] Refreshing screenshot...
[[11:41:32]] [INFO] Executing Multi Step action step 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[11:41:31]] [SUCCESS] Screenshot refreshed successfully
[[11:41:31]] [SUCCESS] Screenshot refreshed
[[11:41:30]] [INFO] Refreshing screenshot...
[[11:41:26]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[11:41:26]] [INFO] Loaded 9 steps from test case: apple health
[[11:41:26]] [INFO] Loading steps for Multi Step action: apple health
[[11:41:26]] [INFO] Executing action 24/25: Execute Test Case: apple health (8 steps)
[[11:41:25]] [SUCCESS] Screenshot refreshed successfully
[[11:41:25]] [SUCCESS] Screenshot refreshed
[[11:41:24]] [INFO] Refreshing screenshot...
[[11:41:20]] [INFO] Executing action 23/25: Terminate app: com.apple.Health
[[11:41:20]] [SUCCESS] Screenshot refreshed successfully
[[11:41:20]] [SUCCESS] Screenshot refreshed
[[11:41:19]] [INFO] Refreshing screenshot...
[[11:41:16]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[11:41:15]] [SUCCESS] Screenshot refreshed successfully
[[11:41:15]] [SUCCESS] Screenshot refreshed
[[11:41:15]] [INFO] Refreshing screenshot...
[[11:41:13]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[11:41:13]] [SUCCESS] Screenshot refreshed successfully
[[11:41:13]] [SUCCESS] Screenshot refreshed
[[11:41:12]] [INFO] Refreshing screenshot...
[[11:41:10]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[11:41:09]] [SUCCESS] Screenshot refreshed successfully
[[11:41:09]] [SUCCESS] Screenshot refreshed
[[11:41:08]] [INFO] Refreshing screenshot...
[[11:41:05]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[11:41:05]] [SUCCESS] Screenshot refreshed successfully
[[11:41:05]] [SUCCESS] Screenshot refreshed
[[11:41:04]] [INFO] Refreshing screenshot...
[[11:41:01]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[11:41:01]] [SUCCESS] Screenshot refreshed successfully
[[11:41:01]] [SUCCESS] Screenshot refreshed
[[11:41:00]] [INFO] Refreshing screenshot...
[[11:40:48]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[11:40:47]] [SUCCESS] Screenshot refreshed successfully
[[11:40:47]] [SUCCESS] Screenshot refreshed
[[11:40:46]] [SUCCESS] Screenshot refreshed successfully
[[11:40:46]] [INFO] Refreshing screenshot...
[[11:40:46]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[11:40:46]] [INFO] Executing action 16/25: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[11:40:46]] [SUCCESS] Screenshot refreshed
[[11:40:44]] [INFO] Refreshing screenshot...
[[11:40:44]] [SUCCESS] Screenshot refreshed successfully
[[11:40:44]] [SUCCESS] Screenshot refreshed
[[11:40:43]] [INFO] Refreshing screenshot...
[[11:40:40]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[11:40:40]] [SUCCESS] Screenshot refreshed successfully
[[11:40:40]] [SUCCESS] Screenshot refreshed
[[11:40:38]] [INFO] Refreshing screenshot...
[[11:40:35]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[11:40:34]] [SUCCESS] Screenshot refreshed successfully
[[11:40:34]] [SUCCESS] Screenshot refreshed
[[11:40:33]] [INFO] Refreshing screenshot...
[[11:40:31]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[11:40:30]] [SUCCESS] Screenshot refreshed successfully
[[11:40:30]] [SUCCESS] Screenshot refreshed
[[11:40:30]] [INFO] Refreshing screenshot...
[[11:40:28]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[11:40:27]] [SUCCESS] Screenshot refreshed successfully
[[11:40:27]] [SUCCESS] Screenshot refreshed
[[11:40:27]] [INFO] Refreshing screenshot...
[[11:40:23]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[11:40:23]] [SUCCESS] Screenshot refreshed successfully
[[11:40:23]] [SUCCESS] Screenshot refreshed
[[11:40:22]] [INFO] Refreshing screenshot...
[[11:40:21]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[11:40:20]] [SUCCESS] Screenshot refreshed successfully
[[11:40:20]] [SUCCESS] Screenshot refreshed
[[11:40:20]] [INFO] Refreshing screenshot...
[[11:40:17]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[11:40:17]] [SUCCESS] Screenshot refreshed successfully
[[11:40:17]] [SUCCESS] Screenshot refreshed
[[11:40:16]] [INFO] Refreshing screenshot...
[[11:40:14]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[11:40:14]] [SUCCESS] Screenshot refreshed successfully
[[11:40:14]] [SUCCESS] Screenshot refreshed
[[11:40:13]] [INFO] Refreshing screenshot...
[[11:40:08]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[11:40:08]] [INFO] Loaded 9 steps from test case: health2
[[11:40:08]] [INFO] Loading steps for Multi Step action: health2
[[11:40:08]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[11:40:08]] [SUCCESS] Screenshot refreshed successfully
[[11:40:08]] [SUCCESS] Screenshot refreshed
[[11:40:06]] [INFO] Refreshing screenshot...
[[11:40:04]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[11:40:04]] [SUCCESS] Screenshot refreshed successfully
[[11:40:04]] [SUCCESS] Screenshot refreshed
[[11:40:02]] [INFO] Refreshing screenshot...
[[11:39:59]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[11:39:58]] [SUCCESS] Screenshot refreshed successfully
[[11:39:58]] [SUCCESS] Screenshot refreshed
[[11:39:58]] [INFO] Refreshing screenshot...
[[11:39:54]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[11:39:54]] [SUCCESS] Screenshot refreshed successfully
[[11:39:54]] [SUCCESS] Screenshot refreshed
[[11:39:53]] [INFO] Refreshing screenshot...
[[11:39:51]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[11:39:50]] [SUCCESS] Screenshot refreshed successfully
[[11:39:50]] [SUCCESS] Screenshot refreshed
[[11:39:50]] [INFO] Refreshing screenshot...
[[11:39:37]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[11:39:36]] [SUCCESS] Screenshot refreshed successfully
[[11:39:36]] [SUCCESS] Screenshot refreshed
[[11:39:35]] [INFO] Refreshing screenshot...
[[11:39:33]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[11:39:32]] [SUCCESS] Screenshot refreshed successfully
[[11:39:32]] [SUCCESS] Screenshot refreshed
[[11:39:31]] [INFO] Refreshing screenshot...
[[11:39:27]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[11:39:27]] [SUCCESS] Screenshot refreshed successfully
[[11:39:27]] [SUCCESS] Screenshot refreshed
[[11:39:26]] [INFO] Refreshing screenshot...
[[11:39:24]] [INFO] Executing action 7/25: Wait for 1 ms
[[11:39:23]] [SUCCESS] Screenshot refreshed successfully
[[11:39:23]] [SUCCESS] Screenshot refreshed
[[11:39:22]] [INFO] Refreshing screenshot...
[[11:39:21]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[11:39:20]] [SUCCESS] Screenshot refreshed successfully
[[11:39:20]] [SUCCESS] Screenshot refreshed
[[11:39:20]] [INFO] Refreshing screenshot...
[[11:39:16]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[11:39:16]] [SUCCESS] Screenshot refreshed successfully
[[11:39:16]] [SUCCESS] Screenshot refreshed
[[11:39:15]] [INFO] Refreshing screenshot...
[[11:39:14]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[11:39:13]] [SUCCESS] Screenshot refreshed successfully
[[11:39:13]] [SUCCESS] Screenshot refreshed
[[11:39:13]] [INFO] Refreshing screenshot...
[[11:39:10]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[11:39:10]] [SUCCESS] Screenshot refreshed successfully
[[11:39:10]] [SUCCESS] Screenshot refreshed
[[11:39:09]] [INFO] Refreshing screenshot...
[[11:39:07]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[11:39:07]] [SUCCESS] Screenshot refreshed successfully
[[11:39:07]] [SUCCESS] Screenshot refreshed
[[11:39:06]] [INFO] Refreshing screenshot...
[[11:39:03]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[11:39:03]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[11:39:03]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[11:39:03]] [INFO] Clearing screenshots from database before execution...
[[11:39:03]] [SUCCESS] All screenshots deleted successfully
[[11:39:03]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[11:39:03]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_113903/screenshots
[[11:39:03]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250613_113903
[[11:39:03]] [SUCCESS] Report directory initialized successfully
[[11:39:03]] [INFO] Initializing report directory and screenshots folder...
[[11:39:01]] [SUCCESS] All screenshots deleted successfully
[[11:39:01]] [INFO] All actions cleared
[[11:39:01]] [INFO] Cleaning up screenshots...
[[11:38:56]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[11:38:56]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[11:38:53]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[11:38:50]] [SUCCESS] Found 1 device(s)
[[11:38:50]] [INFO] Refreshing device list...
