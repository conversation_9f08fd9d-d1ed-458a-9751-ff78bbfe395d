#!/usr/bin/env python3
"""
Test script to verify multi-device port configuration works correctly
"""

import sys
import os
import requests
import time
import subprocess
import argparse
from pathlib import Path

# Add app directory to path
app_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app')
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

def test_port_configuration(flask_port, appium_port, wda_port):
    """Test if the port configuration is working correctly"""
    print(f"Testing port configuration:")
    print(f"  Flask port: {flask_port}")
    print(f"  Appium port: {appium_port}")
    print(f"  WDA port: {wda_port}")
    
    # Test 1: Check if config is updated correctly
    try:
        import config
        print(f"  Config FLASK_PORT: {getattr(config, 'FLASK_PORT', 'Not set')}")
        print(f"  Config APPIUM_PORT: {getattr(config, 'APPIUM_PORT', 'Not set')}")
        print(f"  Config WDA_PORT: {getattr(config, 'WDA_PORT', 'Not set')}")
    except ImportError as e:
        print(f"  Error importing config: {e}")
        return False
    
    # Test 2: Check if AppiumDeviceController uses correct ports
    try:
        from app.utils.appium_device_controller import AppiumDeviceController
        controller = AppiumDeviceController(appium_port=appium_port, wda_port=wda_port)
        print(f"  Controller Appium port: {controller.appium_port}")
        print(f"  Controller WDA port: {controller.wda_port}")
        
        if controller.appium_port != appium_port:
            print(f"  ERROR: Controller Appium port mismatch!")
            return False
        if controller.wda_port != wda_port:
            print(f"  ERROR: Controller WDA port mismatch!")
            return False
            
    except Exception as e:
        print(f"  Error testing AppiumDeviceController: {e}")
        return False
    
    print("  ✅ Port configuration test passed!")
    return True

def test_flask_server_response(port):
    """Test if Flask server is responding on the specified port"""
    try:
        url = f"http://localhost:{port}"
        print(f"Testing Flask server at {url}...")
        
        # Give server time to start
        max_retries = 10
        for i in range(max_retries):
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    print(f"  ✅ Flask server responding on port {port}")
                    return True
            except requests.exceptions.ConnectionError:
                if i < max_retries - 1:
                    print(f"  Waiting for server to start... ({i+1}/{max_retries})")
                    time.sleep(2)
                else:
                    print(f"  ❌ Flask server not responding on port {port}")
                    return False
            except Exception as e:
                print(f"  Error testing Flask server: {e}")
                return False
        
        return False
    except Exception as e:
        print(f"Error testing Flask server: {e}")
        return False

def test_appium_server_response(port):
    """Test if Appium server is responding on the specified port"""
    try:
        url = f"http://localhost:{port}/wd/hub/status"
        print(f"Testing Appium server at {url}...")
        
        # Give server time to start
        max_retries = 15
        for i in range(max_retries):
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    print(f"  ✅ Appium server responding on port {port}")
                    return True
            except requests.exceptions.ConnectionError:
                if i < max_retries - 1:
                    print(f"  Waiting for Appium server to start... ({i+1}/{max_retries})")
                    time.sleep(3)
                else:
                    print(f"  ❌ Appium server not responding on port {port}")
                    return False
            except Exception as e:
                print(f"  Error testing Appium server: {e}")
                return False
        
        return False
    except Exception as e:
        print(f"Error testing Appium server: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Test multi-device port configuration')
    parser.add_argument('--flask-port', type=int, default=8081, help='Flask server port to test')
    parser.add_argument('--appium-port', type=int, default=4724, help='Appium server port to test')
    parser.add_argument('--wda-port', type=int, default=8101, help='WDA port to test')
    parser.add_argument('--test-servers', action='store_true', help='Test if servers are actually running')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("Multi-Device Port Configuration Test")
    print("=" * 60)
    
    # Set environment variables to simulate command line arguments
    os.environ['FLASK_PORT'] = str(args.flask_port)
    os.environ['APPIUM_PORT'] = str(args.appium_port)
    os.environ['WDA_PORT'] = str(args.wda_port)
    
    # Test port configuration
    config_test_passed = test_port_configuration(args.flask_port, args.appium_port, args.wda_port)
    
    if args.test_servers:
        print("\nTesting server responses...")
        flask_test_passed = test_flask_server_response(args.flask_port)
        appium_test_passed = test_appium_server_response(args.appium_port)
        
        print("\n" + "=" * 60)
        print("Test Results:")
        print(f"  Port Configuration: {'✅ PASSED' if config_test_passed else '❌ FAILED'}")
        print(f"  Flask Server: {'✅ PASSED' if flask_test_passed else '❌ FAILED'}")
        print(f"  Appium Server: {'✅ PASSED' if appium_test_passed else '❌ FAILED'}")
        
        if config_test_passed and flask_test_passed and appium_test_passed:
            print("\n🎉 All tests passed! Multi-device configuration is working correctly.")
            return 0
        else:
            print("\n❌ Some tests failed. Please check the configuration.")
            return 1
    else:
        print("\n" + "=" * 60)
        print("Test Results:")
        print(f"  Port Configuration: {'✅ PASSED' if config_test_passed else '❌ FAILED'}")
        
        if config_test_passed:
            print("\n✅ Port configuration test passed!")
            print("To test with running servers, use: --test-servers")
            return 0
        else:
            print("\n❌ Port configuration test failed.")
            return 1

if __name__ == '__main__':
    sys.exit(main())
