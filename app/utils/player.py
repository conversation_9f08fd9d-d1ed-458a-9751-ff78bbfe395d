import time
import os
import json
import traceback
import logging
from datetime import datetime

def get_action_factory(device_controller):
    """Helper function to get ActionFactory with proper import handling"""
    try:
        # Try relative import first
        from ..actions.action_factory import ActionFactory
        return ActionFactory(device_controller)
    except ImportError:
        try:
            # Try absolute import as fallback
            import sys
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
            from actions.action_factory import ActionFactory
            return ActionFactory(device_controller)
        except ImportError as e:
            logging.getLogger("Player").error(f"Failed to import ActionFactory: {e}")
            return None

class Player:
    """Player for replaying recorded actions on an Android device"""

    def __init__(self, device_controller, test_cases_dir, socketio=None, test_idx=None):
        """Initialize with a device controller and optional socketio instance"""
        self.device_controller = device_controller
        self.test_cases_dir = test_cases_dir
        self.socketio = socketio
        self.is_playing = False
        self.execution_stopped = False
        self.current_action_index = 0
        self.actions = []
        self.wait_time = 30  # Default wait time in seconds
        self.default_interval = 1  # Default interval between checks
        self.playback_callback = None
        self.current_test_case_name = None

        # Track current test case boundaries within a test suite
        self.current_test_case_start_index = 0
        self.current_test_case_end_index = -1  # -1 means not set/use all actions

        # Store test_idx if provided during initialization
        if test_idx is not None:
            self.current_test_idx = test_idx
            # Initialize logger before using it
            self.logger = logging.getLogger("Player")
            self.logger.info(f"Player initialized with test_idx={test_idx}")
        else:
            # Try to get the current test index from app.py
            try:
                from app.app import current_test_idx
                self.current_test_idx = current_test_idx.value
                # Initialize logger before using it
                self.logger = logging.getLogger("Player")
                self.logger.info(f"Player initialized with current_test_idx.value={current_test_idx.value}")
            except Exception as e:
                # Initialize logger before using it
                self.logger = logging.getLogger("Player")
                self.logger.error(f"Error getting current test index during initialization: {str(e)}")
                self.current_test_idx = 0  # Default to 0 if there's an error
                self.logger.info(f"Player initialized with default test_idx=0")

        # Initialize logger if not already done
        if not hasattr(self, 'logger'):
            self.logger = logging.getLogger("Player")

    def load_recording(self, file_path):
        """Load recording from a file"""
        try:
            with open(file_path, 'r') as f:
                self.actions = json.load(f)
            return True
        except Exception as e:
            print(f"Error loading recording: {e}")
            return False

    def play(self, actions=None, delay=0.5, callback=None, suite_id=None, test_case_start_index=0, test_case_end_index=-1, test_idx=None):
        """Play back the recorded actions"""
        # Set the actions and callback
        if actions:
            self.actions = actions
        self.playback_callback = callback
        self.suite_id = suite_id  # Store the suite_id for use in screenshots

        # Set the current test case boundaries
        self.current_test_case_start_index = test_case_start_index
        self.current_test_case_end_index = test_case_end_index
        self.logger.info(f"Setting test case boundaries: start={test_case_start_index}, end={test_case_end_index}")

        # Import the current test index for logging and store it locally
        try:
            from app.app import current_test_idx

            # If test_idx is explicitly provided, use it and update the global value
            if test_idx is not None:
                self.current_test_idx = test_idx
                current_test_idx.value = test_idx  # Update the global value
                self.logger.info(f"DEBUG: Using provided test_idx: {test_idx}")
            else:
                # Otherwise use the global value
                self.current_test_idx = current_test_idx.value

            self.logger.info(f"DEBUG: Current test index at start of play method: {current_test_idx.value}")
            self.logger.info(f"DEBUG: Stored local test index: {self.current_test_idx}")
        except Exception as e:
            self.logger.error(f"Error getting current test index: {str(e)}")
            # If test_idx is explicitly provided, use it even if there's an error with the global
            if test_idx is not None:
                self.current_test_idx = test_idx
                self.logger.info(f"DEBUG: Using provided test_idx after error: {test_idx}")
            else:
                self.current_test_idx = 0  # Default to 0 if there's an error

        # We now use only the database for tracking failures
        self.logger.info("Using database for tracking failures in the current test case")

        # Reset execution tracking and failure status for this test case
        try:
            # Reset the global variable that tracks if the current test case has failures
            import app.app
            app.app.current_test_case_has_failures = False
            self.logger.info("Reset global current_test_case_has_failures = False for new test case")

            # Also reset the database tracking for completeness
            from app.utils.database import reset_test_case_execution_tracking
            if suite_id:
                # Get the current test index from app.py
                from app.app import current_test_idx
                test_idx = current_test_idx.value if hasattr(current_test_idx, 'value') else 0

                # Reset execution tracking for this test case
                reset_test_case_execution_tracking(suite_id, test_idx)
                self.logger.info(f"Reset execution tracking for test case {test_idx} in suite {suite_id}")
        except Exception as reset_error:
            self.logger.error(f"Error resetting execution tracking: {str(reset_error)}")

        # Check if we have actions to play
        if not self.actions:
            return False, "No actions to play"

        # Start playback
        self.is_playing = True
        total_actions = len(self.actions)
        self.current_action_index = 0
        success = True
        message = "All actions executed successfully"

        # Emit start event
        if self.socketio:
            self.socketio.emit('action_result', {
                'status': 'started',
                'total_actions': total_actions,
                'message': f'Starting execution of {total_actions} actions.'
            })

        # Track session failures for auto-recovery
        session_failures = 0
        max_session_failures = 3

        # Reset execution_stopped flag
        self.execution_stopped = False

        try:
            # First verify device connection before starting
            if not self._verify_device_connection():
                self.logger.error("Device session not active before starting execution")
                if not self._recover_device_connection():
                    return False, "Failed to establish device connection before execution"

            # Process actions one at a time for better reliability
            for i in range(total_actions):
                if not self.is_playing or self.execution_stopped:
                    message = "Playback stopped by user"
                    if self.socketio:
                        self.socketio.emit('action_result', {
                            'status': 'stopped',
                            'action_index': i,
                            'total_actions': total_actions,
                            'message': message,
                            'completed': True
                        })
                    break

                action = self.actions[i]
                self.current_action_index = i

                # Call the callback with current progress
                if self.playback_callback:
                    self.playback_callback({
                        'status': 'playing',
                        'current': i + 1,
                        'total': total_actions,
                        'action': action
                    })

                # Log current action
                action_type = action.get('type', 'unknown')
                action_id = action.get('action_id', '')

                # Include action_id in the log message if available
                if action_id:
                    self.logger.info(f"Executing action {i+1}/{total_actions}: {action_type} (action_id: {action_id})")
                else:
                    self.logger.info(f"Executing action {i+1}/{total_actions}: {action_type}")

                # Emit running event before execution attempt
                if self.socketio:
                    message = f'Executing action {i+1}/{total_actions}: {action_type}'
                    if action_id:
                        message += f' (action_id: {action_id})'

                    self.socketio.emit('action_result', {
                        'status': 'running',
                        'action_index': i,
                        'total_actions': total_actions,
                        'action_type': action_type,
                        'message': message,
                        'action_id': action_id  # Include action_id as a separate field
                    })

                # Only verify device connection every 5 actions to improve performance
                if i % 5 == 0:
                    self.logger.info("Checking device connection (every 5 actions)")
                    if not self._verify_device_connection():
                        self.logger.error("Device connection lost before executing action. Attempting recovery...")
                        session_failures += 1

                        if session_failures > max_session_failures:
                            self.logger.error(f"Too many session failures ({session_failures}). Aborting execution.")
                            success = False
                            message = f"Aborted after {session_failures} session failures"
                            if self.socketio:
                                self.socketio.emit('action_result', {
                                    'status': 'error',
                                    'action_index': i,
                                    'total_actions': total_actions,
                                    'message': message,
                                    'completed': True
                                })
                            break

                        if not self._recover_device_connection():
                            self.logger.error("Recovery failed. Trying one more recovery with delay...")
                            # Try one more time with a longer delay
                            time.sleep(2) # Reduced from 5 seconds to 2 seconds
                            if not self._recover_device_connection():
                                success = False
                                message = f"Failed at action {i+1}/{total_actions}: Device connection could not be recovered"
                                if self.socketio:
                                    self.socketio.emit('action_result', {
                                        'status': 'error',
                                        'action_index': i,
                                        'total_actions': total_actions,
                                        'message': message,
                                        'completed': True
                                    })
                                break

                        self.logger.info("Device connection recovered successfully. Continuing execution.")

                # Execute the action with retry
                max_retries = 2  # Retry once on failure
                action_success = False # Initialize action success flag for this iteration
                action_message = "Action execution not attempted" # Default message
                screenshot_url = None # Initialize screenshot url

                # Handle Hook Actions - they should be skipped during normal execution
                # but we need to make sure they're properly recognized
                if action_type == 'hookAction':
                    self.logger.info(f"Skipping Hook Action during normal execution")
                    # Log the hook action details for debugging
                    hook_type = action.get('hook_type', '')
                    hook_data = action.get('hook_data', {})
                    self.logger.info(f"Hook action details - type: {hook_type}, data: {hook_data}")

                    action_success = True
                    action_message = "Hook Action skipped (will only be executed when a step fails)"
                    continue

                for retry in range(max_retries):
                    try:
                        result = self.execute_action(action)

                        if isinstance(result, tuple) and len(result) == 3:
                            action_success, action_message, screenshot_path = result
                            # Convert screenshot_path to URL if available
                            if screenshot_path:
                                screenshot_url = f"/screenshots/{os.path.basename(screenshot_path)}?t={int(time.time())}"
                                self.logger.info(f"Generated screenshot URL from path: {screenshot_url}")
                        elif isinstance(result, dict):
                            # Handle legacy format
                            action_success = result.get('status') == 'success' or result.get('success', False)
                            action_message = result.get('message', 'Unknown result')
                            screenshot_path = None
                            # Check if screenshot url is in the result dict
                            screenshot_url = result.get('screenshot_url') or result.get('screenshot') # Handle both keys
                            # Get screenshot_filename if available
                            screenshot_filename = result.get('screenshot_filename')
                            if screenshot_url:
                                self.logger.info(f"Using screenshot URL from result dict: {screenshot_url}")
                        else:
                            action_success = False
                            action_message = "Invalid action result format"
                            screenshot_path = None

                        # If action succeeded, break the retry loop
                        if action_success:
                            # Reset session failure counter after a successful action
                            session_failures = 0
                            # Emit success event for this action
                            if self.socketio:
                                # Create the event data
                                emit_data = {
                                    'status': 'success',
                                    'action_index': i,
                                    'total_actions': total_actions,
                                    'message': action_message,
                                    'screenshot': screenshot_url,
                                    'completed': False,
                                    'action_id': action_id  # Include action_id as a separate field
                                }

                                # Add screenshot_filename if available
                                if 'screenshot_filename' in locals() and screenshot_filename:
                                    emit_data['screenshot_filename'] = screenshot_filename
                                    self.logger.info(f"Including screenshot_filename in socket event: {screenshot_filename}")

                                self.socketio.emit('action_result', emit_data)
                            break

                        # If action failed and we have retries left, attempt recovery and retry
                        if retry < max_retries - 1:
                            self.logger.warning(f"Action failed, attempting recovery before retry: {action_message}")
                            if "session" in action_message.lower() or "driver" in action_message.lower():
                                # This looks like a session error, increment counter
                                session_failures += 1

                            if self._recover_device_connection():
                                self.logger.info("Device connection recovered, retrying action")
                                # Add a short delay before retry
                                time.sleep(1)
                                continue

                        # If we reach here, the action failed after all retries
                        self.logger.error(f"Action {i+1}/{total_actions} ({action_type}) failed after {retry+1} attempts: {action_message}")
                        success = False
                        message = f"Failed at action {i+1}/{total_actions}: {action_message}"
                        # Track failure in database instead of using last_error
                        try:
                            from app.utils.database import track_test_execution
                            from app.app import current_test_idx, current_step_idx, current_suite_id, current_test_case_has_failures

                            # Set the global variable to indicate the current test case has failures
                            # Track failures in the database
                            import app.app
                            app.app.current_test_case_has_failures = True
                            self.logger.info("Set global current_test_case_has_failures = True")

                            # Log the current test index for debugging
                            self.logger.info(f"DEBUG: Before tracking failure - current_test_idx.value: {current_test_idx.value}")

                            # First check if test_idx is provided in the action
                            action_test_idx = action.get('test_idx')
                            if action_test_idx is not None:
                                local_test_idx = action_test_idx
                                self.logger.info(f"DEBUG: Using test_idx from action: {local_test_idx} for tracking failure")
                            else:
                                # Use the locally stored test_idx if available, otherwise use current_test_idx.value
                                local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                                self.logger.info(f"DEBUG: Using local_test_idx: {local_test_idx} for tracking failure")

                            track_test_execution(
                                suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                                test_idx=local_test_idx,
                                step_idx=current_step_idx.value,
                                filename=action.get('filename', 'unknown'),
                                status='failed',
                                in_progress=False,
                                action_type=action_type,
                                action_params=action,
                                error=action_message
                            )
                        except Exception as track_error:
                            self.logger.error(f"Error tracking failure in database: {str(track_error)}")

                        if self.socketio:
                            self.socketio.emit('action_result', {
                                'status': 'error',
                                'action_index': i,
                                'total_actions': total_actions,
                                'message': message,
                                'completed': False
                            })
                        break

                    except Exception as e:
                        action_success = False
                        action_message = f"Exception during action execution: {str(e)}"
                        screenshot_path = None
                        screenshot_url = None # No screenshot on exception

                        # Check if this is a session error
                        if "session" in str(e).lower() or "driver" in str(e).lower() or "nosuch" in str(e).lower():
                            session_failures += 1

                        # Similar retry logic for exceptions
                        if retry < max_retries - 1:
                            self.logger.warning(f"Action failed with exception, attempting recovery: {str(e)}")
                            if self._recover_device_connection():
                                self.logger.info("Device connection recovered, retrying action")
                                # Add a short delay before retry
                                time.sleep(1)
                                continue

                        self.logger.error(f"Action {i+1}/{total_actions} ({action_type}) failed with exception: {str(e)}")
                        success = False
                        message = f"Failed at action {i+1}/{total_actions}: {str(e)}"
                        # Track failure in database instead of using last_error
                        try:
                            from app.utils.database import track_test_execution
                            from app.app import current_test_idx, current_step_idx, current_suite_id, current_test_case_has_failures

                            # Set the global variable to indicate the current test case has failures
                            # Track failures in the database
                            import app.app
                            app.app.current_test_case_has_failures = True
                            self.logger.info("Set global current_test_case_has_failures = True")

                            # Log the current test index for debugging
                            self.logger.info(f"DEBUG: Before tracking exception - current_test_idx.value: {current_test_idx.value}")

                            # First check if test_idx is provided in the action
                            action_test_idx = action.get('test_idx')
                            if action_test_idx is not None:
                                local_test_idx = action_test_idx
                                self.logger.info(f"DEBUG: Using test_idx from action: {local_test_idx} for tracking exception")
                            else:
                                # Use the locally stored test_idx if available, otherwise use current_test_idx.value
                                local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                                self.logger.info(f"DEBUG: Using local_test_idx: {local_test_idx} for tracking exception")

                            track_test_execution(
                                suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                                test_idx=local_test_idx,
                                step_idx=current_step_idx.value,
                                filename=action.get('filename', 'unknown'),
                                status='failed',
                                in_progress=False,
                                action_type=action_type,
                                action_params=action,
                                error=str(e)
                            )
                        except Exception as track_error:
                            self.logger.error(f"Error tracking exception in database: {str(track_error)}")

                        if self.socketio:
                            self.socketio.emit('action_result', {
                                'status': 'error',
                                'action_index': i,
                                'total_actions': total_actions,
                                'message': message,
                                'completed': False
                            })
                        break

                # If action failed after all retries, check for hook actions
                if not action_success:
                    # We now use the database for tracking failures instead of last_error

                    # Look for all hook actions in the test case
                    hook_actions = self._find_hook_action()

                    # If we found any hook actions, execute them in sequence and retry the failed action
                    if hook_actions:
                        self.logger.info(f"Found {len(hook_actions)} hook actions. Executing them in sequence for recovery...")

                        # Track if any hook action succeeded
                        any_hook_succeeded = False

                        # Execute each hook action in sequence
                        for hook_index, hook_action in enumerate(hook_actions):
                            hook_type = hook_action.get('hook_type', 'unknown')
                            hook_action_id = hook_action.get('action_id', '')

                            # Include action_id in the log message if available
                            if hook_action_id:
                                self.logger.info(f"Executing hook action {hook_index+1}/{len(hook_actions)}: {hook_type} (action_id: {hook_action_id})")
                            else:
                                self.logger.info(f"Executing hook action {hook_index+1}/{len(hook_actions)}: {hook_type}")

                            # Emit hook action execution event
                            if self.socketio:
                                message = f"Executing hook action {hook_index+1}/{len(hook_actions)}: {hook_type}"
                                if hook_action_id:
                                    message += f" (action_id: {hook_action_id})"

                                self.socketio.emit('action_result', {
                                    'status': 'hook_action',
                                    'action_index': i,
                                    'total_actions': total_actions,
                                    'message': message,
                                    'completed': False,
                                    'action_id': hook_action_id  # Include action_id as a separate field
                                })

                            # Execute the hook action
                            hook_success, hook_message, _ = self._execute_hook_action(hook_action)

                            if hook_success:
                                self.logger.info(f"Hook action {hook_index+1}/{len(hook_actions)} executed successfully: {hook_message}")
                                any_hook_succeeded = True
                            else:
                                self.logger.error(f"Hook action {hook_index+1}/{len(hook_actions)} execution failed: {hook_message}")

                        # After executing all hook actions, retry the failed action if at least one hook action succeeded
                        if any_hook_succeeded:
                            # Retry the failed action
                            self.logger.info(f"Retrying failed action after executing all hook actions...")

                            # Emit retry event
                            if self.socketio:
                                self.socketio.emit('action_result', {
                                    'status': 'retrying',
                                    'action_index': i,
                                    'total_actions': total_actions,
                                    'message': f"Retrying action after executing all hook actions",
                                    'completed': False,
                                    'action_id': action_id  # Include action_id as a separate field
                                })

                            # Wait a moment before retrying
                            time.sleep(1)

                            # Retry the action
                            try:
                                result = self.execute_action(action)

                                if isinstance(result, tuple) and len(result) >= 2:
                                    action_success = result[0]
                                    action_message = result[1]
                                    if len(result) > 2:
                                        screenshot_url = result[2]
                                elif isinstance(result, dict):
                                    action_success = result.get('status') == 'success'
                                    action_message = result.get('message', '')
                                    screenshot_url = result.get('screenshot')

                                if action_success:
                                    self.logger.info(f"Action succeeded after hook actions recovery: {action_message}")
                                    # Send success event to update UI - explicitly mark as hook_recovery_success
                                    if self.socketio:
                                        self.socketio.emit('action_result', {
                                            'status': 'success',
                                            'action_index': i,
                                            'total_actions': total_actions,
                                            'message': f"Action succeeded after hook actions recovery: {action_message}",
                                            'screenshot': screenshot_url,
                                            'completed': False,
                                            'hook_recovery_success': True,  # Special flag to indicate success after hook recovery
                                            'action_id': action_id  # Include action_id as a separate field
                                        })
                                    # Continue with the next action since this one succeeded after recovery
                                    continue
                                else:
                                    self.logger.error(f"Action still failed after hook actions recovery: {action_message}")
                            except Exception as retry_error:
                                self.logger.error(f"Error retrying action after hook actions: {str(retry_error)}")
                                action_success = False
                                action_message = f"Error retrying action: {str(retry_error)}"
                        else:
                            self.logger.error(f"All hook actions failed, cannot recover the failed action")

                    # Hook action handling is complete



                    # Hook action handling is now done above

                    # If we reach here, either there was no hook action or the hook action failed
                    if self.socketio:
                        self.socketio.emit('action_result', {
                            'status': 'error',
                            'action_index': i,
                            'total_actions': total_actions,
                            'message': action_message,
                            'screenshot': screenshot_url,
                            'completed': False,
                            'action_id': action_id  # Include action_id as a separate field
                        })
                    # Break the main action loop if an action failed
                    success = False # Mark overall execution as failed
                    message = action_message # Use the specific error message
                    self.logger.error(f"Action failed: {message}. Stopping execution.")
                    break

                # Removed stabilization delay to improve performance
                # This will break some tests that rely on the delay, but will improve execution time

                # Minimal delay between actions - reduced to 0.1 seconds for better performance
                # Skip delays for wait/assertion actions that have built-in delays
                if i < total_actions - 1 and action_type not in ['wait', 'assertion', 'waitTill']:
                    # Use a much shorter delay to improve performance
                    time.sleep(0.1)

            # Call the callback with final status
            if self.playback_callback:
                self.playback_callback({
                    'status': 'completed' if success else 'failed',
                    'current': self.current_action_index + 1,
                    'total': total_actions,
                    'message': message
                })

            # Emit final completion event
            if self.socketio:
                self.socketio.emit('action_result', {
                    'status': 'completed' if success else 'failed',
                    'action_index': self.current_action_index,
                    'total_actions': total_actions,
                    'message': message,
                    'completed': True
                })

            return success, message

        except Exception as e:
            error_msg = f"Error during playback: {str(e)}"
            self.logger.error(error_msg)
            traceback.print_exc()

            if self.playback_callback:
                self.playback_callback({
                    'status': 'error',
                    'message': error_msg,
                    'current': self.current_action_index + 1,
                    'total': total_actions
                })
            # Emit final error event
            if self.socketio:
                self.socketio.emit('action_result', {
                    'status': 'error',
                    'action_index': self.current_action_index,
                    'total_actions': total_actions,
                    'message': error_msg,
                    'completed': True
                })
            return False, error_msg
        finally:
            self.is_playing = False

    def _verify_device_connection(self):
        """Verify that the device connection is still active"""
        if not self.device_controller or not hasattr(self.device_controller, 'driver'):
            return False

        try:
            # A simple check to see if the driver is still responsive
            driver = self.device_controller.driver
            if not driver:
                return False

            # Try to get a simple property to check if driver is responsive
            # Use proper exception handling to catch NoSuchSessionErrors
            try:
                activity = driver.current_activity
                return True
            except Exception as session_error:
                error_msg = str(session_error).lower()
                if any(err in error_msg for err in ["nosuchdriver", "no such session", "session is either terminated"]):
                    self.logger.error(f"Session has been terminated: {session_error}")
                    return False
                else:
                    # Some other error that might not mean the session is completely dead
                    self.logger.warning(f"Unusual error during connection check: {session_error}")
                    # Try an alternative method to check if connection is alive
                    try:
                        source = driver.page_source
                        return True
                    except:
                        return False
        except Exception as e:
            self.logger.warning(f"Device connection check failed: {str(e)}")
            return False

    def _recover_device_connection(self):
        """Attempt to recover the device connection after a failure"""
        try:
            # Log the recovery attempt
            self.logger.info("Attempting to recover device connection")

            # First check if the device object is available
            if not self.device_controller:
                self.logger.error("No device controller available for recovery")
                return False

            # Check if the device has a reconnect method
            if hasattr(self.device_controller, 'reconnect_device'):
                # Log device information before reconnection attempt
                device_id = getattr(self.device_controller, 'device_id', None)
                platform_name = getattr(self.device_controller, 'platform_name', None)
                self.logger.info(f"Current device information before reconnection: id={device_id}, platform={platform_name}")

                # First try a forced disconnect to ensure clean state
                try:
                    self.device_controller.disconnect()
                except Exception as disconnect_error:
                    self.logger.warning(f"Error during forced disconnect: {disconnect_error}")

                # Add a delay to ensure previous session is fully terminated
                time.sleep(3)

                # Verify device information is preserved after disconnect
                device_id_after = getattr(self.device_controller, 'device_id', None)
                platform_name_after = getattr(self.device_controller, 'platform_name', None)
                self.logger.info(f"Device information after disconnect: id={device_id_after}, platform={platform_name_after}")

                # If device information was lost, restore it
                if not device_id_after and device_id:
                    self.device_controller.device_id = device_id
                    self.logger.info(f"Restored device_id: {device_id}")

                if not platform_name_after and platform_name:
                    self.device_controller.platform_name = platform_name
                    self.logger.info(f"Restored platform_name: {platform_name}")

                # Try to reconnect the device with 3 attempts
                for attempt in range(3):
                    try:
                        self.logger.info(f"Calling reconnect_device() attempt {attempt+1}/3")
                        success = self.device_controller.reconnect_device()
                        if success:
                            self.logger.info(f"Successfully recovered device connection on attempt {attempt+1}")

                            # Verify the connection actually works with a test command
                            if hasattr(self.device_controller, 'driver') and self.device_controller.driver:
                                try:
                                    # Try to get a screenshot as a test
                                    # Use a generic action_id for connection verification screenshots
                                    screenshot_result = self.device_controller.take_screenshot(action_id="al_connection_test")
                                    self.logger.info(f"Verified connection by taking a screenshot: {screenshot_result}")

                                    # Return the screenshot URL if available
                                    if isinstance(screenshot_result, dict) and screenshot_result.get('path'):
                                        screenshot_path = screenshot_result.get('path')
                                        screenshot_url = f"/screenshots/{os.path.basename(screenshot_path)}?t={int(time.time())}"
                                        self.logger.info(f"Generated screenshot URL: {screenshot_url}")
                                        # We'll return True here, the screenshot URL will be used in the execute_action method

                                    return True
                                except Exception as verify_error:
                                    self.logger.warning(f"Connection verification failed: {verify_error}")
                                    # Continue to next attempt
                            elif hasattr(self.device_controller, 'airtest_device') and self.device_controller.airtest_device:
                                try:
                                    # Try to get a screenshot using Airtest
                                    self.device_controller.airtest_device.snapshot()
                                    self.logger.info("Verified connection using Airtest device")
                                    return True
                                except Exception as airtest_error:
                                    self.logger.warning(f"Airtest connection verification failed: {airtest_error}")
                            else:
                                self.logger.warning("Reconnect reported success but no driver or Airtest device available")
                        else:
                            self.logger.warning(f"Failed to recover connection on attempt {attempt+1}")

                        # Add a delay before next attempt
                        time.sleep(2)
                    except Exception as e:
                        self.logger.error(f"Error during reconnection attempt {attempt+1}: {e}")
                        time.sleep(2)

                self.logger.error("All reconnection attempts failed")
                return False
            else:
                # If reconnect_device method doesn't exist, try to implement a basic reconnection
                self.logger.warning("Device controller does not have reconnect_device method, attempting manual reconnection")

                # Store device information before disconnect
                device_id = getattr(self.device_controller, 'device_id', None)
                platform_name = getattr(self.device_controller, 'platform_name', None)
                options = getattr(self.device_controller, 'options', None)
                self.logger.info(f"Stored device information for manual reconnection: id={device_id}, platform={platform_name}")

                # First disconnect
                try:
                    self.device_controller.disconnect()
                except Exception as disconnect_error:
                    self.logger.warning(f"Error during manual disconnect: {disconnect_error}")

                # Wait for resources to be released
                time.sleep(3)

                # Try to reconnect using the same device ID
                if hasattr(self.device_controller, 'connect_to_device') and device_id:
                    try:
                        self.logger.info(f"Attempting manual reconnection to device {device_id} with platform {platform_name}")
                        if self.device_controller.connect_to_device(device_id, options, platform_name):
                            self.logger.info("Manual reconnection successful")
                            return True
                        else:
                            self.logger.error("Manual reconnection failed")
                    except Exception as manual_error:
                        self.logger.error(f"Error during manual reconnection: {manual_error}")
                else:
                    self.logger.error("Device controller does not support reconnection and manual reconnection not possible")

                return False
        except Exception as e:
            self.logger.error(f"Error during device recovery: {str(e)}")
            return False

    def stop(self):
        """Stop playback"""
        self.is_playing = False
        self.execution_stopped = True
        self.logger.info("Playback stopped by user request")

    def _find_hook_action(self):
        """Find all hook actions in the current test case only"""
        hook_actions = []

        # Determine the range of actions to search based on current test case boundaries
        start_idx = self.current_test_case_start_index
        end_idx = self.current_test_case_end_index

        # If end_idx is -1 or invalid, use the entire actions list
        if end_idx < 0 or end_idx >= len(self.actions):
            end_idx = len(self.actions)

        # Log the search range for debugging
        self.logger.info(f"Searching for hook actions in current test case only: actions[{start_idx}:{end_idx}]")

        # Only search within the current test case's actions
        for i in range(start_idx, end_idx):
            action = self.actions[i]
            if action.get('type') == 'hookAction':
                hook_actions.append(action)
                self.logger.info(f"Found hook action at index {i}: {action.get('hook_type', 'unknown')}")

        self.logger.info(f"Found {len(hook_actions)} hook actions in current test case")
        return hook_actions



    def _convert_hook_to_action(self, hook_action):
        """Convert a hook action to a regular action for execution"""
        if not hook_action or hook_action.get('type') != 'hookAction':
            self.logger.error(f"Invalid hook action: {hook_action}")
            return None

        # Create a new action based on the hook type and data
        hook_type = hook_action.get('hook_type')
        hook_data = hook_action.get('hook_data', {})

        if not hook_type:
            self.logger.error(f"Hook action missing hook_type: {hook_action}")
            return None

        # Create a new action with the hook type and data
        recovery_action = {
            'type': hook_type
        }

        # Handle specific action types
        if hook_type == 'tap':
            method = hook_data.get('method', 'coordinates')
            recovery_action['method'] = method

            if method == 'locator':
                recovery_action['locator_type'] = hook_data.get('locator_type')
                recovery_action['locator_value'] = hook_data.get('locator_value')
                recovery_action['timeout'] = hook_data.get('timeout', 10)
                recovery_action['interval'] = hook_data.get('interval', 0.5)
            elif method == 'image':
                recovery_action['image_filename'] = hook_data.get('image_filename')
                recovery_action['threshold'] = hook_data.get('threshold', 0.7)
                recovery_action['timeout'] = hook_data.get('timeout', 20)
            else:  # coordinates
                recovery_action['x'] = hook_data.get('x', 0)
                recovery_action['y'] = hook_data.get('y', 0)
        elif hook_type == 'tapOnText':
            recovery_action['text'] = hook_data.get('text', '')
            recovery_action['timeout'] = hook_data.get('timeout', 10)
            recovery_action['threshold'] = hook_data.get('threshold', 0.7)
        elif hook_type == 'wait':
            recovery_action['duration'] = hook_data.get('duration', 5)
        elif hook_type == 'text':
            recovery_action['text'] = hook_data.get('text', '')
        elif hook_type == 'sendKeys':
            recovery_action['locator_type'] = hook_data.get('locator_type', '')
            recovery_action['locator_value'] = hook_data.get('locator_value', '')
            recovery_action['text'] = hook_data.get('text', '')
            recovery_action['clear_first'] = hook_data.get('clear_first', False)
            recovery_action['timeout'] = hook_data.get('timeout', 15)
        elif hook_type == 'swipe':
            recovery_action['start_x'] = hook_data.get('start_x', 0)
            recovery_action['start_y'] = hook_data.get('start_y', 0)
            recovery_action['end_x'] = hook_data.get('end_x', 0)
            recovery_action['end_y'] = hook_data.get('end_y', 0)
            recovery_action['duration'] = hook_data.get('duration', 300)
            recovery_action['count'] = hook_data.get('count', 1)
            recovery_action['interval'] = hook_data.get('interval', 0.5)
        elif hook_type == 'iosFunctions':
            recovery_action['function_name'] = hook_data.get('function_name', '')
            # Add other iOS function parameters
            for key, value in hook_data.items():
                if key != 'function_name':
                    recovery_action[key] = value
        elif hook_type == 'launchApp' or hook_type == 'restartApp' or hook_type == 'terminateApp':
            recovery_action['package_id'] = hook_data.get('package_id', '')
        else:
            # For other action types, just copy all hook_data
            for key, value in hook_data.items():
                recovery_action[key] = value

        self.logger.info(f"Converted hook action to: {recovery_action}")
        return recovery_action

    def _execute_hook_action(self, hook_action):
        """Execute a hook action for recovery"""
        if not hook_action or hook_action.get('type') != 'hookAction':
            return False, "Invalid hook action", None

        # Log detailed information about the hook action
        hook_type = hook_action.get('hook_type', 'unknown')
        hook_data = hook_action.get('hook_data', {})

        # Create a more detailed log message with the specific hook action details
        hook_details = ""
        if hook_type == 'tap':
            if 'image_filename' in hook_data:
                hook_details = f"tap on image '{hook_data.get('image_filename')}'"
            elif 'locator_type' in hook_data and 'locator_value' in hook_data:
                hook_details = f"tap on element with {hook_data.get('locator_type')}: '{hook_data.get('locator_value')}'"
            elif 'x' in hook_data and 'y' in hook_data:
                hook_details = f"tap at coordinates ({hook_data.get('x')}, {hook_data.get('y')})"
        elif hook_type == 'wait':
            hook_details = f"wait for {hook_data.get('duration', 1)}s"
        elif hook_type == 'text':
            hook_details = f"input text '{hook_data.get('text', '')}'"
        elif hook_type == 'sendKeys':
            hook_details = f"send keys '{hook_data.get('text', '')}' to {hook_data.get('locator_type', '')}: '{hook_data.get('locator_value', '')}'"
        elif hook_type == 'swipe':
            hook_details = f"swipe from ({hook_data.get('start_x', 0)}, {hook_data.get('start_y', 0)}) to ({hook_data.get('end_x', 0)}, {hook_data.get('end_y', 0)})"
        elif hook_type == 'iosFunctions':
            hook_details = f"iOS function '{hook_data.get('function_name', '')}'"

        self.logger.info(f"Executing hook action: {hook_type} - {hook_details}")
        self.logger.info(f"Full hook action details: {hook_action}")

        try:
            # Add a small delay before executing the hook action to ensure the UI is stable
            time.sleep(0.5)

            # Use the action factory to execute the hook action
            action_factory = get_action_factory(self.device_controller)
            if not action_factory:
                return False, "Failed to initialize ActionFactory", None

            # Convert the hook action to a regular action
            converted_action = self._convert_hook_to_action(hook_action)
            if not converted_action:
                self.logger.error("Failed to convert hook action to regular action")
                return False, "Failed to convert hook action", None

            self.logger.info(f"Executing hook action via ActionFactory with action type: {hook_type}")

            # Execute the action directly using its type (e.g., 'tap') instead of 'hookAction'
            result = action_factory.execute_action(hook_type, converted_action)

            # Process the result
            if isinstance(result, dict):
                success = result.get('status') == 'success' or result.get('success', False)
                message = result.get('message', '')
                screenshot_url = result.get('screenshot')
                hook_details_from_result = result.get('hook_details', hook_details)

                if success:
                    # Create a more detailed success message
                    detailed_message = f"Hook action executed successfully: {hook_type}"
                    if hook_details_from_result:
                        detailed_message += f" - {hook_details_from_result}"
                    self.logger.info(detailed_message)
                else:
                    self.logger.error(f"Hook action execution failed: {message}")

                # Return as a tuple for compatibility with existing code
                return success, detailed_message if success else message, screenshot_url
            elif isinstance(result, tuple) and len(result) >= 2:
                success = result[0]
                message = result[1]
                screenshot_url = result[2] if len(result) > 2 else None

                if success:
                    # Create a more detailed success message
                    detailed_message = f"Hook action executed successfully: {hook_type}"
                    if hook_details:
                        detailed_message += f" - {hook_details}"
                    self.logger.info(detailed_message)
                else:
                    self.logger.error(f"Hook action execution failed: {message}")

                # Return the tuple
                return success, detailed_message if success else message, screenshot_url
            else:
                # If result is not a tuple or dict, convert it to a standard format
                success = bool(result)
                message = "Hook action executed successfully" if success else "Hook action failed"
                return success, message, None
        except Exception as e:
            self.logger.error(f"Error executing hook action: {str(e)}")
            traceback.print_exc()
            return False, f"Hook action execution failed: {str(e)}", None

    def execute_action(self, action):
        """Execute a specific action on the device"""
        if not hasattr(self, 'logger'):
            import logging
            self.logger = logging.getLogger('player')

        self.logger.info(f"Executing action: {action}")
        screenshot_path = None

        # Get the action type
        action_type = action.get('type', 'unknown')

        # Import the current test index for logging
        try:
            from app.app import current_test_idx
            self.logger.info(f"DEBUG: Current test index at start of execute_action: {current_test_idx.value}")
            self.logger.info(f"DEBUG: Player instance ID: {id(self)}, has current_test_idx: {hasattr(self, 'current_test_idx')}")
            if hasattr(self, 'current_test_idx'):
                self.logger.info(f"DEBUG: Player's current_test_idx value: {self.current_test_idx}")
        except Exception as e:
            self.logger.error(f"Error getting current test index: {str(e)}")

        # For multiStep actions, we'll use the database to track status
        if action_type == 'multiStep':
            self.logger.info("Using database for tracking failures in multiStep action")

        # Track execution in the database
        try:
            # Import the database tracking function
            from app.utils.database import track_test_execution

            # Import the current test and step indices from app.py
            from app.app import current_test_idx, current_step_idx, current_suite_id

            # Get the test case filename if available
            filename = action.get('filename', 'unknown')

            # Get the action_id if available
            action_id = action.get('action_id', '')
            if action_id:
                self.logger.info(f"DEBUG: Using action_id from action: {action_id}")

            # Get the test_idx from the current_test_idx global variable
            # But log it first for debugging
            self.logger.info(f"DEBUG: Before tracking - current_test_idx.value: {current_test_idx.value}")

            # First check if test_idx is provided in the action
            action_test_idx = action.get('test_idx')
            if action_test_idx is not None:
                local_test_idx = action_test_idx
                self.logger.info(f"DEBUG: Using test_idx from action: {local_test_idx} for tracking")
                # Also update our internal test_idx to match
                self.current_test_idx = local_test_idx
                self.logger.info(f"DEBUG: Updated player's current_test_idx to match action: {self.current_test_idx}")
                # Also update the global current_test_idx
                current_test_idx.value = local_test_idx
                self.logger.info(f"DEBUG: Updated global current_test_idx.value to match action: {local_test_idx}")
            else:
                # Use the locally stored test_idx if available, otherwise use current_test_idx.value
                local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                self.logger.info(f"DEBUG: Using local_test_idx: {local_test_idx} for tracking")

            # Add a very distinctive log message to help debug test_idx issues
            self.logger.info(f"========== PLAYER EXECUTING ACTION WITH TEST_IDX: {local_test_idx} ==========")
            self.logger.info(f"========== ACTION TYPE: {action.get('type', 'Unknown')} ==========")
            self.logger.info(f"========== ACTION ID: {action_id} ==========")
            self.logger.info(f"========== GLOBAL CURRENT_TEST_IDX: {current_test_idx.value} ==========")
            self.logger.info(f"========== PLAYER CURRENT_TEST_IDX: {getattr(self, 'current_test_idx', 'Not Set')} ==========")

            # Track the execution with action details
            track_test_execution(
                suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                test_idx=local_test_idx,
                step_idx=current_step_idx.value,
                filename=filename,
                status='running',
                in_progress=True,
                action_type=action_type,
                action_params=action,
                action_id=action_id
            )

            self.logger.info(f"Tracked execution in database: test_idx={local_test_idx}, step_idx={current_step_idx.value}, action_type={action_type}, action_id={action_id}")
        except Exception as e:
            self.logger.error(f"Error tracking execution in database: {str(e)}")

        # Removed device connection verification before each action to improve performance
        # This will break some tests that rely on the verification, but will improve execution time
        self.logger.info("Skipping device connection verification for better performance")

        try:
            # Extract action type and parameters
            action_type = action.get('type', '')

            # Handle numeric action types or string containing numbers
            if isinstance(action_type, (int, float)) or (isinstance(action_type, str) and action_type.isdigit()):
                # Convert numeric type to string
                self.logger.warning(f"Converting numeric action type {action_type} to string")

                # Map numeric types to action types
                action_type_map = {
                    '1': 'tap',
                    '2': 'text',
                    '3': 'swipe',
                    '4': 'wait',
                    '5': 'key',
                    '6': 'doubleTap',
                    '7': 'tapAndType',
                    '8': 'tapOnText',
                    '9': 'sendKeys',
                    '10': 'addLog'
                }

                # Convert to string if it's a number
                if isinstance(action_type, (int, float)):
                    action_type = str(int(action_type))

                # Use the mapping or default to 'tap'
                if action_type in action_type_map:
                    action_type = action_type_map[action_type]
                    self.logger.info(f"Converted numeric action type to: {action_type}")
                    # Update the action object with the string type
                    action['type'] = action_type
                else:
                    self.logger.error(f"Unknown numeric action type: {action_type}")
                    return False, f"Unknown action type: {action_type}", None

            # Handle Hook Actions - they should be skipped during normal execution
            # but we need to make sure they're properly recognized
            if action_type == 'hookAction':
                self.logger.info(f"Skipping Hook Action during normal execution")
                # Use the action factory to validate the hook action
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Log the hook action details for debugging
                hook_type = action.get('hook_type', '')
                hook_data = action.get('hook_data', {})
                self.logger.info(f"Hook action details - type: {hook_type}, data: {hook_data}")

                # Just validate the action but don't execute it
                return True, "Hook Action skipped (will only be executed when a step fails)", None

            # Apply parameter substitution to text fields in the action
            try:
                from app.utils.parameter_utils import substitute_parameters

                # Apply parameter substitution for text input actions
                if action_type == 'text' and 'text' in action:
                    original_text = action['text']
                    substituted_text = substitute_parameters(original_text)

                    # Log the substitution if it occurred
                    if substituted_text != original_text:
                        self.logger.info(f"Parameter substitution in Player.execute_action: '{original_text}' -> '{substituted_text}'")
                        # Update the action with the substituted text
                        action['text'] = substituted_text

                    # Check if we need to generate random data
                    if 'data_generator' in action and action['data_generator'] and action['data_generator'] != 'none':
                        self.logger.info(f"Generating random data using generator: {action['data_generator']}")

                        # Generate random data using the specified generator
                        try:
                            from app.utils.random_data_generator import generate_data
                            generated_data = generate_data(action['data_generator'])

                            if generated_data:
                                self.logger.info(f"Generated random data: '{generated_data}'")
                                # Update the action with the generated data
                                action['text'] = generated_data
                            else:
                                self.logger.warning(f"Failed to generate random data using generator: {action['data_generator']}")
                        except Exception as gen_error:
                            self.logger.error(f"Error generating random data: {str(gen_error)}")
                            # Continue with the original/substituted text

                # Apply parameter substitution for sendKeys actions
                elif action_type == 'sendKeys' and 'text' in action:
                    original_text = action['text']
                    substituted_text = substitute_parameters(original_text)

                    # Log the substitution if it occurred
                    if substituted_text != original_text:
                        self.logger.info(f"Parameter substitution in Player.execute_action: '{original_text}' -> '{substituted_text}'")
                        # Update the action with the substituted text
                        action['text'] = substituted_text

                    # Check if we need to generate random data
                    if 'data_generator' in action and action['data_generator'] and action['data_generator'] != 'none':
                        self.logger.info(f"Generating random data using generator: {action['data_generator']}")

                        # Generate random data using the specified generator
                        try:
                            from app.utils.random_data_generator import generate_data
                            generated_data = generate_data(action['data_generator'])

                            if generated_data:
                                self.logger.info(f"Generated random data: '{generated_data}'")
                                # Update the action with the generated data
                                action['text'] = generated_data
                            else:
                                self.logger.warning(f"Failed to generate random data using generator: {action['data_generator']}")
                        except Exception as gen_error:
                            self.logger.error(f"Error generating random data: {str(gen_error)}")
                            # Continue with the original/substituted text

                # Apply parameter substitution for tapAndType actions
                elif action_type == 'tapAndType' and 'text' in action:
                    original_text = action['text']
                    substituted_text = substitute_parameters(original_text)

                    # Log the substitution if it occurred
                    if substituted_text != original_text:
                        self.logger.info(f"Parameter substitution in Player.execute_action: '{original_text}' -> '{substituted_text}'")
                        # Update the action with the substituted text
                        action['text'] = substituted_text

                    # Check if we need to generate random data
                    if 'data_generator' in action and action['data_generator'] and action['data_generator'] != 'none':
                        self.logger.info(f"Generating random data using generator: {action['data_generator']}")

                        # Generate random data using the specified generator
                        try:
                            from app.utils.random_data_generator import generate_data
                            generated_data = generate_data(action['data_generator'])

                            if generated_data:
                                self.logger.info(f"Generated random data: '{generated_data}'")
                                # Update the action with the generated data
                                action['text'] = generated_data
                            else:
                                self.logger.warning(f"Failed to generate random data using generator: {action['data_generator']}")
                        except Exception as gen_error:
                            self.logger.error(f"Error generating random data: {str(gen_error)}")
                            # Continue with the original/substituted text

                # Apply parameter substitution for compareValue actions
                elif action_type == 'compareValue' and 'expected_value' in action:
                    original_text = action['expected_value']
                    substituted_text = substitute_parameters(original_text)

                    # Log the substitution if it occurred
                    if substituted_text != original_text:
                        self.logger.info(f"Parameter substitution in Player.execute_action: '{original_text}' -> '{substituted_text}'")
                        # Update the action with the substituted text
                        action['expected_value'] = substituted_text
            except Exception as e:
                self.logger.error(f"Error applying parameter substitution: {str(e)}")

            # Handle different action types
            if action_type == 'tap':
                # Check if we're using a locator
                locator_type = action.get('locator_type')
                locator_value = action.get('locator_value')
                fallback_locators = action.get('fallback_locators', [])

                # Check for new fallback mechanism
                fallback_type = action.get('fallback_type')

                # Log fallback information
                if fallback_type:
                    self.logger.info(f"Action has fallback_type: {fallback_type}")

                    # Log fallback details based on type
                    if fallback_type == 'coordinates':
                        self.logger.info(f"Fallback coordinates: ({action.get('fallback_x')}, {action.get('fallback_y')})")
                    elif fallback_type == 'image':
                        self.logger.info(f"Fallback image: {action.get('fallback_image_filename')}")
                    elif fallback_type == 'text':
                        self.logger.info(f"Fallback text: {action.get('fallback_text')}")
                    elif fallback_type == 'locator':
                        self.logger.info(f"Fallback locator: {action.get('fallback_locator_type')}={action.get('fallback_locator_value')}")

                if locator_type and locator_value:
                    timeout = int(action.get('timeout', 10))
                    interval = float(action.get('interval', 0.5))
                    self.logger.info(f"Tapping on element with {locator_type}: {locator_value}, timeout={timeout}s")

                    # Create fallback parameters if available
                    fallback_params = None
                    if fallback_type:
                        fallback_params = {}

                        if fallback_type == 'coordinates':
                            fallback_params['fallback_x'] = action.get('fallback_x')
                            fallback_params['fallback_y'] = action.get('fallback_y')
                        elif fallback_type == 'image':
                            fallback_params['fallback_image_filename'] = action.get('fallback_image_filename')
                            fallback_params['fallback_threshold'] = action.get('fallback_threshold', 0.7)
                        elif fallback_type == 'text':
                            fallback_params['fallback_text'] = action.get('fallback_text')
                        elif fallback_type == 'locator':
                            fallback_params['fallback_locator_type'] = action.get('fallback_locator_type')
                            fallback_params['fallback_locator_value'] = action.get('fallback_locator_value')

                    # Execute tap with locator and fallback support
                    result = self._execute_tap_with_locator(
                        locator_type,
                        locator_value,
                        timeout,
                        interval,
                        fallback_locators,
                        fallback_type,
                        fallback_params
                    )

                # Check if we have only fallback locators but no primary locator
                elif fallback_locators:
                    timeout = int(action.get('timeout', 10))
                    interval = float(action.get('interval', 0.5))
                    self.logger.info(f"No primary locator, using {len(fallback_locators)} fallback locators")
                    result = self._execute_tap_with_locator(None, None, timeout, interval, fallback_locators)
                # Check if we're using an image reference
                elif action.get('image_filename'):
                    image_filename = action.get('image_filename')
                    threshold = float(action.get('threshold', 0.7))
                    timeout = int(action.get('timeout', 20))
                    self.logger.info(f"Tapping on image: {image_filename} with threshold={threshold}, timeout={timeout}s")
                    # Use the action factory to execute the action
                    from actions.action_factory import ActionFactory
                    action_factory = ActionFactory(self.device_controller)
                    params = {
                        'image_filename': image_filename,
                        'threshold': threshold,
                        'timeout': timeout
                    }
                    result = action_factory.execute_action('tap', params)
                else:
                    # Regular coordinate-based tap
                    x = int(float(action.get('x', 0)))
                    y = int(float(action.get('y', 0)))
                    result = self._execute_tap(x, y)

            elif action_type == 'doubleTap':
                # Check if we're using a locator
                locator_type = action.get('locator_type')
                locator_value = action.get('locator_value')
                fallback_locators = action.get('fallback_locators', [])

                if locator_type and locator_value:
                    timeout = int(action.get('timeout', 10))
                    interval = float(action.get('interval', 0.5))
                    self.logger.info(f"Double-tapping on element with {locator_type}: {locator_value}, timeout={timeout}s")
                    self.logger.info(f"Fallback locators: {len(fallback_locators)}")
                    result = self._execute_double_tap_with_locator(locator_type, locator_value, timeout, interval, fallback_locators)
                # Check if we have only fallback locators but no primary locator
                elif fallback_locators:
                    timeout = int(action.get('timeout', 10))
                    interval = float(action.get('interval', 0.5))
                    self.logger.info(f"No primary locator, using {len(fallback_locators)} fallback locators for double tap")
                    result = self._execute_double_tap_with_locator(None, None, timeout, interval, fallback_locators)
                # Check if we're using an image reference
                elif action.get('image_filename'):
                    image_filename = action.get('image_filename')
                    threshold = float(action.get('threshold', 0.7))
                    timeout = int(action.get('timeout', 20))
                    self.logger.info(f"Double-tapping on image: {image_filename} with threshold={threshold}, timeout={timeout}s")
                    # Use the action factory to execute the action
                    from actions.action_factory import ActionFactory
                    action_factory = ActionFactory(self.device_controller)

                    # Log available action types for debugging
                    self.logger.info(f"Available action types before doubleTap: {sorted(list(action_factory.action_handlers.keys()))}")

                    params = {
                        'image_filename': image_filename,
                        'threshold': threshold,
                        'timeout': timeout
                    }
                    self.logger.info(f"Executing doubleTap action with params: {params}")
                    result = action_factory.execute_action('doubleTap', params)
                    self.logger.info(f"doubleTap action result: {result}")
                else:
                    # Regular coordinate-based double tap
                    x = int(float(action.get('x', 0)))
                    y = int(float(action.get('y', 0)))
                    self.logger.info(f"Double-tapping at: ({x}, {y})")
                    result = self._execute_double_click(x, y)  # Reuse the double click implementation

            elif action_type == 'swipe':
                # Check if we have vector-based coordinates (new format)
                if 'vector_start' in action and 'vector_end' in action:
                    # Get screen dimensions to convert percentage to pixels
                    try:
                        screen_size = self.device_controller.get_device_dimensions()
                        if not screen_size:
                            # Fallback to common screen resolution if can't get actual dimensions
                            screen_size = (1080, 1920)  # Default fallback size

                        # Convert percentage vectors to absolute coordinates
                        start_x = int(action['vector_start'][0] * screen_size[0])
                        start_y = int(action['vector_start'][1] * screen_size[1])
                        end_x = int(action['vector_end'][0] * screen_size[0])
                        end_y = int(action['vector_end'][1] * screen_size[1])
                    except Exception as e:
                        self.logger.error(f"Error converting vector coordinates to absolute: {e}")
                        # Fallback to center screen swipe
                        start_x, start_y = 500, 1000
                        end_x, end_y = 500, 500
                else:
                    # Legacy format - use absolute coordinates directly
                    start_x = int(float(action.get('start_x', 0)))
                    start_y = int(float(action.get('start_y', 0)))
                    end_x = int(float(action.get('end_x', 0)))
                    end_y = int(float(action.get('end_y', 0)))

                # Get the duration, count and interval
                duration = int(float(action.get('duration', 300)))
                count = int(action.get('count', 1))
                interval = float(action.get('interval', 0.5))

                # Execute the swipe(s)
                result = self._execute_multiple_swipes(start_x, start_y, end_x, end_y, duration, count, interval)

            elif action_type == 'text':
                text = action.get('text', '')
                result = self._execute_text(text)

            elif action_type == 'sendKeys':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for sendKeys action
                params = {
                    'locator_type': action.get('locator_type', ''),
                    'locator_value': action.get('locator_value', ''),
                    'text': action.get('text', ''),
                    'clear_first': action.get('clear_first', False),
                    'timeout': action.get('timeout', 15)
                }

                self.logger.info(f"Forwarding sendKeys action to ActionFactory with params: {params}")
                result = action_factory.execute_action('sendKeys', params)

            elif action_type == 'key':
                key_code = int(action.get('key_code', 0))
                result = self._execute_key(key_code)

            elif action_type == 'wait':
                duration = float(action.get('duration', 1))
                result = self._execute_wait(duration)

            elif action_type == 'waitTill':
                # Get locator type to determine which method to use
                locator_type = action.get('locator_type', 'image')
                timeout = float(action.get('timeout', 10))
                interval = float(action.get('interval', 0.5))

                if locator_type == 'image':
                    # Try multiple possible image parameter names
                    image = action.get('image', '')

                    # If image is empty, try other possible parameter names
                    if not image:
                        image = action.get('image_filename', '')

                    # If still empty, try locator_value
                    if not image:
                        image = action.get('locator_value', '')

                    # Log all parameters for debugging
                    self.logger.info(f"Wait Till Image parameters: image={action.get('image')}, " +
                                    f"image_filename={action.get('image_filename')}, " +
                                    f"locator_value={action.get('locator_value')}")

                    self.logger.info(f"Executing Wait Till Image: {image} with timeout={timeout}, interval={interval}")
                    result = self._execute_wait_till(image, timeout, interval)
                else:
                    locator_value = action.get('locator_value', '')
                    self.logger.info(f"Executing Wait Till Element with {locator_type}: {locator_value}, timeout={timeout}")
                    result = self._execute_wait_till_element(locator_type, locator_value, timeout, interval)

            elif action_type == 'exists':
                locator_type = action.get('locator_type', '')
                locator_value = action.get('locator_value', '')
                timeout = int(action.get('timeout', 10))

                self.logger.info(f"Checking if element exists with {locator_type}: {locator_value}, timeout={timeout}s")

                if locator_type == 'image':
                    # For backward compatibility with old exists actions that only had image parameter
                    result = self._execute_exists_image(locator_value)
                else:
                    result = self._execute_exists_element(locator_type, locator_value, timeout)

            elif action_type == 'launchApp':
                # Support both 'package' and 'package_id' fields for backward compatibility
                package = action.get('package', action.get('package_id', ''))
                self.logger.info(f"Launching app: {package}")
                result = self._execute_launch_app(package)

            elif action_type == 'terminateApp':
                # Support both 'package' and 'package_id' fields for backward compatibility
                package = action.get('package', action.get('package_id', ''))
                self.logger.info(f"Terminating app: {package}")
                result = self._execute_terminate_app(package)

            elif action_type == 'restartApp':
                # Support both 'package' and 'package_id' fields for backward compatibility
                package = action.get('package', action.get('package_id', ''))
                self.logger.info(f"Restarting app: {package}")
                result = self._execute_restart_app(package)

            elif action_type == 'doubleClick':
                x = int(float(action.get('x', 0)))
                y = int(float(action.get('y', 0)))
                self.logger.info(f"Double-clicking at: ({x}, {y})")
                result = self._execute_double_click(x, y)

            elif action_type == 'textClear':
                text = action.get('text', '')
                delay = int(action.get('delay', 500))

                # Check if we need to generate random data
                if 'data_generator' in action and action['data_generator'] and action['data_generator'] != 'none':
                    self.logger.info(f"Generating random data using generator: {action['data_generator']}")

                    # Generate random data using the specified generator
                    try:
                        from app.utils.random_data_generator import generate_data
                        generated_data = generate_data(action['data_generator'])

                        if generated_data:
                            self.logger.info(f"Generated random data: '{generated_data}'")
                            # Update the text with the generated data
                            text = generated_data
                        else:
                            self.logger.warning(f"Failed to generate random data using generator: {action['data_generator']}")
                    except Exception as gen_error:
                        self.logger.error(f"Error generating random data: {str(gen_error)}")

                self.logger.info(f"Inputting and clearing text: {text} with delay {delay}ms")
                result = self._execute_text_clear(text, delay)

            elif action_type == 'clickElement':
                locator_type = action.get('locator_type', '')
                locator_value = action.get('locator_value', '')
                timeout = int(action.get('timeout', 10))
                self.logger.info(f"Clicking element with {locator_type}: {locator_value}, timeout={timeout}s")
                result = self._execute_click_element(locator_type, locator_value, timeout)

            elif action_type == 'doubleClickImage':
                image_path = action.get('image_path', '')
                threshold = float(action.get('threshold', 0.7))
                timeout = int(action.get('timeout', 20))
                self.logger.info(f"Double-clicking on image: {image_path} with threshold={threshold}, timeout={timeout}s")
                result = self._execute_double_click_image(image_path, threshold, timeout)

            elif action_type == 'clickImageAirtest':
                image_filename = action.get('image_filename', '')
                threshold = float(action.get('threshold', 0.7))
                timeout = int(action.get('timeout', 20))
                self.logger.info(f"Clicking on image (Airtest): {image_filename} with threshold={threshold}, timeout={timeout}s")
                # Use the action factory to execute the action
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)
                params = {
                    'image_path': image_filename,
                    'threshold': threshold,
                    'timeout': timeout
                }
                result = action_factory.execute_action('click_image', params)

            elif action_type == 'waitImageAirtest':
                # Try multiple possible image parameter names
                image_filename = action.get('image_filename', '')

                # If image_filename is empty, try other possible parameter names
                if not image_filename:
                    image_filename = action.get('image', '')

                # If still empty, try locator_value
                if not image_filename:
                    image_filename = action.get('locator_value', '')

                # Log all parameters for debugging
                self.logger.info(f"Wait Image Airtest parameters: image_filename={action.get('image_filename')}, " +
                                f"image={action.get('image')}, " +
                                f"locator_value={action.get('locator_value')}")

                threshold = float(action.get('threshold', 0.7))
                timeout = int(action.get('timeout', 30))
                interval = float(action.get('interval', 1.0))
                self.logger.info(f"Waiting for image (Airtest): {image_filename} with threshold={threshold}, timeout={timeout}s")

                # Use the wait_till handler but with the image parameter
                if not image_filename:
                    self.logger.error("No image specified for waitImageAirtest action")
                    result = {"status": "error", "message": "No image specified for waitImageAirtest action"}
                else:
                    result = self._execute_wait_till(image_filename, timeout, interval)

            elif action_type == 'doubleClickImageAirtest':
                image_filename = action.get('image_filename', '')
                threshold = float(action.get('threshold', 0.7))
                timeout = int(action.get('timeout', 20))
                self.logger.info(f"Double-clicking on image (Airtest): {image_filename} with threshold={threshold}, timeout={timeout}s")
                # Use the same handler as doubleClickImage but with image_filename as the image_path
                result = self._execute_double_click_image(image_filename, threshold, timeout)

            elif action_type == 'clickImage':
                image_path = action.get('image_path', '')
                threshold = float(action.get('threshold', 0.7))
                timeout = int(action.get('timeout', 20))
                self.logger.info(f"Clicking on image: {image_path} with threshold={threshold}, timeout={timeout}s")
                result = self._execute_click_image(image_path, threshold, timeout)

            elif action_type == 'swipeTillVisible':
                result = self._execute_swipe_till_visible(action)
            elif action_type == 'getValue':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for getValue action
                params = {
                    'locator_type': action.get('locator_type', ''),
                    'locator_value': action.get('locator_value', ''),
                    'attribute': action.get('attribute', 'text'),
                    'timeout': action.get('timeout', 10)
                }

                self.logger.info(f"Forwarding getValue action to ActionFactory with params: {params}")
                result = action_factory.execute_action('getValue', params)

            elif action_type == 'compareValue':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for compareValue action
                params = {
                    'locator_type': action.get('locator_type', ''),
                    'locator_value': action.get('locator_value', ''),
                    'expected_value': action.get('expected_value', ''),
                    'attribute': action.get('attribute', 'text'),
                    'timeout': action.get('timeout', 10),
                    'operation': action.get('operation', 'equals')
                }

                self.logger.info(f"Forwarding compareValue action to ActionFactory with params: {params}")
                result = action_factory.execute_action('compareValue', params)

            elif action_type == 'airplaneMode':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for airplaneMode action
                params = {
                    'enabled': action.get('enabled', False)
                }

                self.logger.info(f"Forwarding airplaneMode action to ActionFactory with params: {params}")
                result = action_factory.execute_action('airplaneMode', params)

            elif action_type == 'hideKeyboard':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                self.logger.info(f"Forwarding hideKeyboard action to ActionFactory")
                result = action_factory.execute_action('hideKeyboard', {})

            elif action_type == 'deviceBack':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                self.logger.info(f"Forwarding deviceBack action to ActionFactory")
                result = action_factory.execute_action('deviceBack', {})

            elif action_type == 'tapAndType':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for tapAndType action
                params = {
                    'text': action.get('text', ''),
                    'timeout': action.get('timeout', 15)
                }

                # Check if we're using coordinates or locator
                method = action.get('method', 'locator')
                params['method'] = method

                if method == 'coordinates':
                    params['x'] = action.get('x')
                    params['y'] = action.get('y')
                else:
                    params['locator_type'] = action.get('locator_type', '')
                    params['locator_value'] = action.get('locator_value', '')

                self.logger.info(f"Forwarding tapAndType action to ActionFactory with params: {params}")
                result = action_factory.execute_action('tapAndType', params)

            elif action_type == 'addMedia':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for addMedia action
                params = {
                    'file_path': action.get('file_path', ''),
                    'destination_path': action.get('destination_path', '')
                }

                self.logger.info(f"Forwarding addMedia action to ActionFactory with params: {params}")
                result = action_factory.execute_action('addMedia', params)

            elif action_type == 'getParam':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for getParam action
                params = {
                    'param_name': action.get('param_name', '')
                }

                self.logger.info(f"Forwarding getParam action to ActionFactory with params: {params}")
                result = action_factory.execute_action('getParam', params)

            elif action_type == 'setParam':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for setParam action
                params = {
                    'param_name': action.get('param_name', ''),
                    'param_value': action.get('param_value', '')
                }

                self.logger.info(f"Forwarding setParam action to ActionFactory with params: {params}")
                result = action_factory.execute_action('setParam', params)

            elif action_type == 'tapIfImageExists':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for tapIfImageExists action
                params = {
                    'image_filename': action.get('image_filename', ''),
                    'threshold': action.get('threshold', 0.7),
                    'timeout': action.get('timeout', 5)
                }

                self.logger.info(f"Forwarding tapIfImageExists action to ActionFactory with params: {params}")
                result = action_factory.execute_action('tapIfImageExists', params)

            elif action_type == 'addLog':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for addLog action
                params = {
                    'message': action.get('message', ''),
                    'take_screenshot': action.get('take_screenshot', True),
                    'action_id': action.get('action_id')  # Pass the action_id from the test case
                }

                # Log the action_id if it exists
                if 'action_id' in action:
                    self.logger.info(f"Using action_id from test case for addLog action: {action.get('action_id')}")

                self.logger.info(f"Forwarding addLog action to ActionFactory with params: {params}")
                result = action_factory.execute_action('addLog', params)

            elif action_type == 'iosFunctions':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for iOS Functions action
                params = {
                    'function_name': action.get('function_name', '')
                }

                # Add function-specific parameters
                if action.get('function_name') == 'press':
                    params['key'] = action.get('key')
                elif action.get('function_name') == 'alert_click':
                    params['button_text'] = action.get('button_text')
                elif action.get('function_name') == 'alert_wait':
                    params['timeout'] = action.get('timeout', 2)
                elif action.get('function_name') == 'set_clipboard':
                    params['content'] = action.get('content')
                elif action.get('function_name') == 'text':
                    params['text'] = action.get('text', '')
                    params['enter'] = action.get('enter', True)
                elif action.get('function_name') == 'push':
                    params['local_path'] = action.get('local_path')
                    if action.get('remote_path'):
                        params['remote_path'] = action.get('remote_path')
                elif action.get('function_name') == 'clear_app':
                    params['bundle_id'] = action.get('bundle_id')

                self.logger.info(f"Forwarding iOS Functions action to ActionFactory with params: {params}")
                result = action_factory.execute_action('iosFunctions', params)

            elif action_type == 'uninstallApp':
                # Forward to the device controller directly
                package_id = action.get('package_id', '')
                self.logger.info(f"Uninstalling app: {package_id}")

                try:
                    # Call the device controller's uninstall_app method directly
                    uninstall_result = self.device_controller.uninstall_app(package_id)

                    # Handle different result formats
                    if isinstance(uninstall_result, dict):
                        # If result is already a dict, return it
                        result = uninstall_result
                    elif uninstall_result is True:
                        # If result is True, return success
                        result = {
                            "status": "success",
                            "message": f"Successfully uninstalled app {package_id}"
                        }
                    else:
                        # For any other result (including False), return error
                        result = {
                            "status": "error",
                            "message": f"Failed to uninstall app {package_id}"
                        }
                except Exception as e:
                    self.logger.error(f"Error uninstalling app {package_id}: {e}")
                    result = {
                        "status": "error",
                        "message": f"Failed to uninstall app: {str(e)}"
                    }

            elif action_type == 'ifElseSteps':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for ifElseSteps action
                params = {
                    'condition_type': action.get('condition_type', ''),
                    'condition': action.get('condition', {}),
                    'then_action': action.get('then_action', {}),
                    'else_action': action.get('else_action', None)
                }

                self.logger.info(f"Forwarding ifElseSteps action to ActionFactory with params: {params}")
                result = action_factory.execute_action('ifElseSteps', params)

            elif action_type == 'tapOnText':
                # Forward to the action factory
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Extract parameters needed for tapOnText action
                params = {
                    'text_to_find': action.get('text_to_find', ''),
                    'timeout': action.get('timeout', 30),
                    'double_tap': action.get('double_tap', False)
                }

                self.logger.info(f"Forwarding tapOnText action to ActionFactory with params: {params}")
                result = action_factory.execute_action('tapOnText', params)

            elif action_type == 'multiStep':
                # Execute a test case as a single action
                test_case_id = action.get('test_case_id', '')
                test_case_name = action.get('test_case_name', 'Unknown Test Case')
                self.logger.info(f"Executing Multi Step action: {test_case_name} (ID: {test_case_id})")

                try:
                    # Check if the test case steps are already embedded in the action
                    test_case_steps = action.get('test_case_steps', [])
                    test_case_actions = [] # Initialize to ensure it's defined

                    if test_case_steps:
                        self.logger.info(f"Using embedded test case steps: {len(test_case_steps)} steps")
                        test_case_actions = test_case_steps
                    else:
                        # If steps are not embedded, try to load them from the test case file
                        self.logger.info(f"No embedded steps found, attempting to load from file: {test_case_id}")

                        if not self.test_cases_dir:
                            self.logger.error("Player does not have test_cases_dir configured. Cannot load multiStep from file.")
                            result = {
                                "status": "error",
                                "message": f"Player misconfiguration: test_cases_dir not set. Cannot load test case {test_case_name}."
                            }
                            # Attempt to update database status before returning
                            try:
                                from app.utils.database import track_test_execution, get_current_test_run_id
                                from app.app import current_test_idx, current_step_idx, current_suite_id
                                action_id = action.get('action_id', '')
                                local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                                current_run_id = get_current_test_run_id(getattr(self, 'suite_id', current_suite_id) or 'unknown', local_test_idx)
                                track_test_execution(
                                    suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                                    test_idx=local_test_idx,
                                    step_idx=current_step_idx.value, # Use global step index
                                    filename=action.get('filename', test_case_name),
                                    status='error',
                                    message=result['message'],
                                    duration='0ms', # Placeholder
                                    screenshot_path=None,
                                    in_progress=False,
                                    action_type=action_type,
                                    action_params=action,
                                    action_id=action_id,
                                    run_id=current_run_id
                                )
                            except Exception as db_track_err:
                                self.logger.error(f"Error updating database for multiStep failure: {db_track_err}")
                            return result # Return after attempting to update DB

                        # First try using the test case manager
                        try:
                            from app.utils.test_case_manager import TestCaseManager
                            # Pass the test_cases_dir from the Player instance
                            test_case_manager = TestCaseManager(test_cases_dir=self.test_cases_dir)
                            test_case = test_case_manager.load_test_case(test_case_id)

                            if test_case and 'actions' in test_case:
                                test_case_actions = test_case.get('actions', [])
                                self.logger.info(f"Loaded {len(test_case_actions)} steps from test case file: {test_case_id}")
                            else:
                                # Fallback to database if file loading fails
                                self.logger.warning(f"Failed to load test case from file '{test_case_id}' using dir '{self.test_cases_dir}', trying database.")
                                from app.utils.database import get_test_case_by_id
                                db_test_case_data = get_test_case_by_id(test_case_id) # Assuming test_case_id might be a DB ID here

                                if not db_test_case_data:
                                    self.logger.error(f"Test case '{test_case_id}' not found in database as fallback.")
                                    result = {
                                        "status": "error",
                                        "message": f"Test case '{test_case_id}' not found in file or database."
                                    }
                                    # Attempt to update DB status
                                    try:
                                        from app.utils.database import track_test_execution, get_current_test_run_id
                                        from app.app import current_test_idx, current_step_idx, current_suite_id
                                        action_id = action.get('action_id', '')
                                        local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                                        current_run_id = get_current_test_run_id(getattr(self, 'suite_id', current_suite_id) or 'unknown', local_test_idx)

                                        track_test_execution(
                                            suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                                            test_idx=local_test_idx,
                                            step_idx=current_step_idx.value,
                                            filename=action.get('filename', test_case_name),
                                            status='error',
                                            message=result['message'],
                                            duration='0ms',
                                            screenshot_path=None,
                                            in_progress=False,
                                            action_type=action_type,
                                            action_params=action,
                                            action_id=action_id,
                                            run_id=current_run_id
                                        )
                                    except Exception as db_track_err:
                                        self.logger.error(f"Error updating database for multiStep failure: {db_track_err}")
                                    return result # Return after attempting to update DB
                                test_case_actions = db_test_case_data.get('actions', [])
                                if test_case_actions:
                                     self.logger.info(f"Loaded {len(test_case_actions)} steps for '{test_case_id}' from database fallback.")
                                else:
                                    self.logger.warning(f"Test case '{test_case_id}' from database fallback has no actions.")

                        except Exception as load_error:
                            self.logger.error(f"Error loading test case from file '{test_case_id}': {load_error}")
                            # Fallback to database if file loading fails
                            self.logger.warning(f"Trying database as fallback for: {test_case_id}")
                            from app.utils.database import get_test_case_by_id
                            db_test_case_data = get_test_case_by_id(test_case_id)

                            if not db_test_case_data:
                                self.logger.error(f"Test case '{test_case_id}' not found in database as fallback after file load exception.")
                                result = {
                                    "status": "error",
                                    "message": f"Test case '{test_case_id}' not found: {str(load_error)}"
                                }
                                # Attempt to update DB status
                                try:
                                    from app.utils.database import track_test_execution, get_current_test_run_id
                                    from app.app import current_test_idx, current_step_idx, current_suite_id
                                    action_id = action.get('action_id', '')
                                    local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                                    current_run_id = get_current_test_run_id(getattr(self, 'suite_id', current_suite_id) or 'unknown', local_test_idx)
                                    track_test_execution(
                                        suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                                        test_idx=local_test_idx,
                                        step_idx=current_step_idx.value,
                                        filename=action.get('filename', test_case_name),
                                        status='error',
                                        message=result['message'],
                                        duration='0ms',
                                        screenshot_path=None,
                                        in_progress=False,
                                        action_type=action_type,
                                        action_params=action,
                                        action_id=action_id,
                                        run_id=current_run_id
                                    )
                                except Exception as db_track_err:
                                    self.logger.error(f"Error updating database for multiStep failure: {db_track_err}")
                                return result # Return after attempting to update DB
                            test_case_actions = db_test_case_data.get('actions', [])
                            if test_case_actions:
                                self.logger.info(f"Loaded {len(test_case_actions)} steps for '{test_case_id}' from database fallback after file load exception.")
                            else:
                                self.logger.warning(f"Test case '{test_case_id}' from database (after exception) has no actions.")


                    if not test_case_actions: # Re-check after all loading attempts
                        self.logger.warning(f"Test case '{test_case_name}' (ID: {test_case_id}) has no actions after all loading attempts. This will be reported as an error.")
                        result = {
                            "status": "error", # CHANGED from "success" to "error"
                            "message": f"Multi-step: Test case '{test_case_name}' (ID: {test_case_id}) found but contains no actions."
                        }
                        # Attempt to update DB status
                        try:
                            from app.utils.database import track_test_execution, get_current_test_run_id
                            from app.app import current_test_idx, current_step_idx, current_suite_id
                            action_id = action.get('action_id', '')
                            local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                            current_run_id = get_current_test_run_id(getattr(self, 'suite_id', current_suite_id) or 'unknown', local_test_idx)
                            track_test_execution(
                                suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                                test_idx=local_test_idx,
                                step_idx=current_step_idx.value,
                                filename=action.get('filename', test_case_name),
                                status='error',
                                message=result['message'],
                                duration='0ms',
                                screenshot_path=None,
                                in_progress=False,
                                action_type=action_type,
                                action_params=action,
                                action_id=action_id,
                                run_id=current_run_id
                            )
                        except Exception as db_track_err:
                            self.logger.error(f"Error updating database for multiStep (no actions) failure: {db_track_err}")
                        return result # Return error status
                    else:
                        # Execute each action in the test case
                        self.logger.info(f"Executing {len(test_case_actions)} actions from test case: {test_case_name}")

                        # Track success/failure
                        all_actions_succeeded = True
                        failure_message = ""

                        # Execute each action
                        for i, step_action in enumerate(test_case_actions):
                            step_type = step_action.get('type', 'unknown')
                            step_action_id = step_action.get('action_id', '')

                            # Include action_id in the log message if available
                            if step_action_id:
                                self.logger.info(f"Executing step {i+1}/{len(test_case_actions)}: {step_type} (action_id: {step_action_id})")
                            else:
                                self.logger.info(f"Executing step {i+1}/{len(test_case_actions)}: {step_type}")

                            # Execute the action
                            step_result = self.execute_action(step_action)

                            # Check if the action succeeded
                            if isinstance(step_result, tuple) and len(step_result) >= 2:
                                step_success, step_message = step_result[0], step_result[1]
                                if not step_success:
                                    all_actions_succeeded = False
                                    failure_message = f"Step {i+1} failed: {step_message}"
                                    self.logger.error(failure_message)
                                    break
                            elif isinstance(step_result, dict):
                                if step_result.get("status") == "error":
                                    all_actions_succeeded = False
                                    failure_message = f"Step {i+1} failed: {step_result.get('message')}"
                                    self.logger.error(failure_message)
                                    break
                            elif not step_result:
                                all_actions_succeeded = False
                                failure_message = f"Step {i+1} failed: Unknown error"
                                self.logger.error(failure_message)
                                break

                            # Add a small delay between steps
                            time.sleep(0.5)

                        # Return the result
                        if all_actions_succeeded:
                            result = {
                                "status": "success",
                                "message": f"Successfully executed test case: {test_case_name}"
                            }
                        else:
                            result = {
                                "status": "error",
                                "message": failure_message
                            }
                except Exception as e:
                    self.logger.error(f"Error executing Multi Step action: {e}")
                    result = {
                        "status": "error",
                        "message": f"Failed to execute Multi Step action: {str(e)}"
                    }

            elif action_type == 'repeatSteps':
                # Execute a test case's steps multiple times
                test_case_id = action.get('test_case_id', '')
                test_case_name = action.get('test_case_name', 'Unknown Test Case')
                repeat_count = action.get('repeat_count', 1)
                
                self.logger.info(f"Executing Repeat Steps action: {test_case_name} (ID: {test_case_id}) - {repeat_count} times")
                
                try:
                    # Check if the test case steps are already embedded in the action
                    test_case_steps = action.get('test_case_steps', [])
                    test_case_actions = [] # Initialize to ensure it's defined
                    
                    if test_case_steps:
                        self.logger.info(f"Using embedded test case steps: {len(test_case_steps)} steps")
                        test_case_actions = test_case_steps
                    else:
                        # If steps are not embedded, try to load them from the test case file
                        self.logger.info(f"No embedded steps found, attempting to load from file: {test_case_id}")
                        
                        if not self.test_cases_dir:
                            self.logger.error("Player does not have test_cases_dir configured. Cannot load repeatSteps from file.")
                            result = {
                                "status": "error",
                                "message": f"Player misconfiguration: test_cases_dir not set. Cannot load test case {test_case_name}."
                            }
                            # Attempt to update database status before returning
                            try:
                                from app.utils.database import track_test_execution, get_current_test_run_id
                                from app.app import current_test_idx, current_step_idx, current_suite_id
                                action_id = action.get('action_id', '')
                                local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                                current_run_id = get_current_test_run_id(getattr(self, 'suite_id', current_suite_id) or 'unknown', local_test_idx)
                                track_test_execution(
                                    suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                                    test_idx=local_test_idx,
                                    step_idx=current_step_idx.value, # Use global step index
                                    filename=action.get('filename', test_case_name),
                                    status='error',
                                    message=result['message'],
                                    duration='0ms', # Placeholder
                                    screenshot_path=None,
                                    in_progress=False,
                                    action_type=action_type,
                                    action_params=action,
                                    action_id=action_id,
                                    run_id=current_run_id
                                )
                            except Exception as db_track_err:
                                self.logger.error(f"Error updating database for repeatSteps failure: {db_track_err}")
                            return result # Return after attempting to update DB
                            
                        # First try using the test case manager
                        try:
                            from app.utils.test_case_manager import TestCaseManager
                            # Pass the test_cases_dir from the Player instance
                            test_case_manager = TestCaseManager(test_cases_dir=self.test_cases_dir)
                            test_case = test_case_manager.load_test_case(test_case_id)
                            
                            if test_case and 'actions' in test_case:
                                test_case_actions = test_case.get('actions', [])
                                self.logger.info(f"Loaded {len(test_case_actions)} steps from test case file: {test_case_id}")
                            else:
                                # Fallback to database if file loading fails
                                self.logger.warning(f"Failed to load test case from file '{test_case_id}' using dir '{self.test_cases_dir}', trying database.")
                                from app.utils.database import get_test_case_by_id
                                db_test_case_data = get_test_case_by_id(test_case_id) # Assuming test_case_id might be a DB ID here
                                
                                if not db_test_case_data:
                                    self.logger.error(f"Test case '{test_case_id}' not found in database as fallback.")
                                    result = {
                                        "status": "error",
                                        "message": f"Test case '{test_case_id}' not found in file or database."
                                    }
                                    # Attempt to update DB status
                                    try:
                                        from app.utils.database import track_test_execution, get_current_test_run_id
                                        from app.app import current_test_idx, current_step_idx, current_suite_id
                                        action_id = action.get('action_id', '')
                                        local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                                        current_run_id = get_current_test_run_id(getattr(self, 'suite_id', current_suite_id) or 'unknown', local_test_idx)
                                        
                                        track_test_execution(
                                            suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                                            test_idx=local_test_idx,
                                            step_idx=current_step_idx.value,
                                            filename=action.get('filename', test_case_name),
                                            status='error',
                                            message=result['message'],
                                            duration='0ms',
                                            screenshot_path=None,
                                            in_progress=False,
                                            action_type=action_type,
                                            action_params=action,
                                            action_id=action_id,
                                            run_id=current_run_id
                                        )
                                    except Exception as db_track_err:
                                        self.logger.error(f"Error updating database for repeatSteps failure: {db_track_err}")
                                    return result # Return after attempting to update DB
                                test_case_actions = db_test_case_data.get('actions', [])
                                if test_case_actions:
                                     self.logger.info(f"Loaded {len(test_case_actions)} steps for '{test_case_id}' from database fallback.")
                                else:
                                    self.logger.warning(f"Test case '{test_case_id}' from database fallback has no actions.")
                                    
                        except Exception as load_error:
                            self.logger.error(f"Error loading test case from file '{test_case_id}': {load_error}")
                            # Fallback to database if file loading fails
                            self.logger.warning(f"Trying database as fallback for: {test_case_id}")
                            from app.utils.database import get_test_case_by_id
                            db_test_case_data = get_test_case_by_id(test_case_id)
                            
                            if not db_test_case_data:
                                self.logger.error(f"Test case '{test_case_id}' not found in database as fallback after file load exception.")
                                result = {
                                    "status": "error",
                                    "message": f"Test case '{test_case_id}' not found: {str(load_error)}"
                                }
                                # Attempt to update DB status
                                try:
                                    from app.utils.database import track_test_execution, get_current_test_run_id
                                    from app.app import current_test_idx, current_step_idx, current_suite_id
                                    action_id = action.get('action_id', '')
                                    local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                                    current_run_id = get_current_test_run_id(getattr(self, 'suite_id', current_suite_id) or 'unknown', local_test_idx)
                                    
                                    track_test_execution(
                                        suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                                        test_idx=local_test_idx,
                                        step_idx=current_step_idx.value,
                                        filename=action.get('filename', test_case_name),
                                        status='error',
                                        message=result['message'],
                                        duration='0ms',
                                        screenshot_path=None,
                                        in_progress=False,
                                        action_type=action_type,
                                        action_params=action,
                                        action_id=action_id,
                                        run_id=current_run_id
                                    )
                                except Exception as db_track_err:
                                    self.logger.error(f"Error updating database for repeatSteps failure: {db_track_err}")
                                return result # Return after attempting to update DB
                            test_case_actions = db_test_case_data.get('actions', [])
                            if test_case_actions:
                                self.logger.info(f"Loaded {len(test_case_actions)} steps for '{test_case_id}' from database fallback after file load exception.")
                            else:
                                self.logger.warning(f"Test case '{test_case_id}' from database (after exception) has no actions.")
                    
                    
                    if not test_case_actions: # Re-check after all loading attempts
                        self.logger.warning(f"Test case '{test_case_name}' (ID: {test_case_id}) has no actions after all loading attempts. This will be reported as an error.")
                        result = {
                            "status": "error",
                            "message": f"Repeat Steps: Test case '{test_case_name}' (ID: {test_case_id}) found but contains no actions."
                        }
                        # Attempt to update DB status
                        try:
                            from app.utils.database import track_test_execution, get_current_test_run_id
                            from app.app import current_test_idx, current_step_idx, current_suite_id
                            action_id = action.get('action_id', '')
                            local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                            current_run_id = get_current_test_run_id(getattr(self, 'suite_id', current_suite_id) or 'unknown', local_test_idx)
                            
                            track_test_execution(
                                suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                                test_idx=local_test_idx,
                                step_idx=current_step_idx.value,
                                filename=action.get('filename', test_case_name),
                                status='error',
                                message=result['message'],
                                duration='0ms',
                                screenshot_path=None,
                                in_progress=False,
                                action_type=action_type,
                                action_params=action,
                                action_id=action_id,
                                run_id=current_run_id
                            )
                        except Exception as db_track_err:
                            self.logger.error(f"Error updating database for repeatSteps (no actions) failure: {db_track_err}")
                        return result # Return error status
                    else:
                        # Execute each action in the test case, repeating for repeat_count times
                        self.logger.info(f"Executing {len(test_case_actions)} actions from test case: {test_case_name} - {repeat_count} times")
                        
                        # Track success/failure
                        all_actions_succeeded = True
                        failure_message = ""
                        
                        # Create an ActionFactory for executing each action
                        from actions.action_factory import ActionFactory
                        action_factory = ActionFactory(self.device_controller)
                        
                        # Execute actions for each repeat
                        for repeat_num in range(1, repeat_count + 1):
                            self.logger.info(f"Starting repeat {repeat_num} of {repeat_count}")
                            
                            # Execute each action in the test case
                            for i, step_action in enumerate(test_case_actions):
                                step_type = step_action.get('type', 'unknown')
                                step_action_id = step_action.get('action_id', '')
                                
                                # Include action_id in the log message if available
                                if step_action_id:
                                    self.logger.info(f"Executing step {i+1}/{len(test_case_actions)} of repeat {repeat_num}/{repeat_count}: {step_type} (action_id: {step_action_id})")
                                else:
                                    self.logger.info(f"Executing step {i+1}/{len(test_case_actions)} of repeat {repeat_num}/{repeat_count}: {step_type}")
                                
                                # Execute the action using the action factory
                                action_type = step_action.get('type', 'unknown')
                                if action_type:
                                    try:
                                        result = action_factory.execute_action(action_type, step_action)
                                        
                                        # Check if the action succeeded
                                        if result.get('status') != 'success':
                                            all_actions_succeeded = False
                                            failure_message = result.get('message', f"Step {i+1} in repeat {repeat_num} failed")
                                            self.logger.error(f"Failed to execute step {i+1} of repeat {repeat_num}: {failure_message}")
                                            
                                            # Try to find a hook action for recovery
                                            hook_action = self._find_hook_action()
                                            if hook_action:
                                                self.logger.info(f"Found hook action for recovery: {hook_action.get('hook_type', 'unknown')}")
                                                try:
                                                    # Convert hook action to a regular action
                                                    hook_type = hook_action.get('hook_type', '')
                                                    recovery_action = self._convert_hook_to_action(hook_action)
                                                    
                                                    # Execute the recovery action
                                                    if hook_type and recovery_action:
                                                        self.logger.info(f"Executing recovery action: {hook_type}")
                                                        hook_result = action_factory.execute_action(hook_type, recovery_action)
                                                        
                                                        if hook_result.get('status') == 'success':
                                                            self.logger.info(f"Recovery action succeeded")
                                                            # We recovered, so continue the test
                                                            all_actions_succeeded = True
                                                            continue
                                                        else:
                                                            self.logger.error(f"Recovery action failed: {hook_result.get('message', 'Unknown reason')}")
                                                    else:
                                                        self.logger.error(f"Could not convert hook action to regular action")
                                                except Exception as hook_error:
                                                    self.logger.error(f"Error executing hook action: {hook_error}")
                                            
                                            # If we get here, either there was no hook action or recovery failed
                                            # Break the innermost loop to stop executing actions in this repeat
                                            break
                                        
                                    except Exception as step_error:
                                        all_actions_succeeded = False
                                        failure_message = f"Exception executing step {i+1} in repeat {repeat_num}: {str(step_error)}"
                                        self.logger.error(failure_message)
                                        
                                        # Try to find a hook action for recovery (same as above)
                                        hook_action = self._find_hook_action()
                                        if hook_action:
                                            self.logger.info(f"Found hook action for recovery: {hook_action.get('hook_type', 'unknown')}")
                                            try:
                                                # Convert hook action to a regular action
                                                hook_type = hook_action.get('hook_type', '')
                                                recovery_action = self._convert_hook_to_action(hook_action)
                                                
                                                # Execute the recovery action
                                                if hook_type and recovery_action:
                                                    self.logger.info(f"Executing recovery action: {hook_type}")
                                                    hook_result = action_factory.execute_action(hook_type, recovery_action)
                                                    
                                                    if hook_result.get('status') == 'success':
                                                        self.logger.info(f"Recovery action succeeded")
                                                        # We recovered, so continue the test
                                                        all_actions_succeeded = True
                                                        continue
                                                    else:
                                                        self.logger.error(f"Recovery action failed: {hook_result.get('message', 'Unknown reason')}")
                                                else:
                                                    self.logger.error(f"Could not convert hook action to regular action")
                                            except Exception as hook_error:
                                                self.logger.error(f"Error executing hook action: {hook_error}")
                                        
                                        # If we get here, either there was no hook action or recovery failed
                                        # Break the innermost loop to stop executing actions in this repeat
                                        break
                                else:
                                    self.logger.warning(f"Step {i+1} in repeat {repeat_num} has no action type, skipping")
                            
                            # If this repeat failed, break out of the repeat loop
                            if not all_actions_succeeded:
                                self.logger.error(f"Repeat {repeat_num} failed, stopping further repeats")
                                break
                            
                            self.logger.info(f"Completed repeat {repeat_num} of {repeat_count} successfully")
                        
                        # Check if all actions executed successfully
                        if all_actions_succeeded:
                            result = {
                                "status": "success",
                                "message": f"Repeat Steps: Executed {len(test_case_actions)} steps from test case '{test_case_name}' {repeat_count} times successfully"
                            }
                        else:
                            result = {
                                "status": "error",
                                "message": failure_message
                            }
                except Exception as e:
                    self.logger.error(f"Error executing Repeat Steps action: {e}")
                    result = {
                        "status": "error",
                        "message": f"Failed to execute Repeat Steps action: {str(e)}"
                    }

            else:
                self.logger.error(f"Unknown action type: {action_type}")
                return False, f"Unknown action type: {action_type}", None

            # Removed fixed delay after action execution to improve performance
            # This will break some tests that rely on the delay, but will improve execution time
            self.logger.info("Skipping delay after action execution for better performance")

            # Screenshots are now taken for addLog actions only, not for reporting actions
            try:
                # Check if this is an addLog action
                is_log_action = action_type == 'addLog'

                # Update the current step index in app.py for proper screenshot naming
                from app.app import current_test_idx, current_step_idx
                # Set the current step index to match the action index (1-based)
                current_step_idx.value = i + 1
                # Get the test index
                test_idx = getattr(current_test_idx, 'value', 0)
                # Get the step index (1-based to match UI display)
                step_idx = getattr(current_step_idx, 'value', 1)

                self.logger.info(f"Current action index: {i}, test_idx: {test_idx}, step_idx: {step_idx}")

                if is_log_action and self.device_controller and hasattr(self.device_controller, 'take_screenshot'):
                    self.logger.info("Taking screenshot for addLog action")

                    # Ensure test_idx and step_idx are integers
                    try:
                        test_idx = int(test_idx)
                        step_idx = int(step_idx)
                    except (ValueError, TypeError):
                        self.logger.warning(f"Invalid test_idx or step_idx values: test_idx={test_idx}, step_idx={step_idx}")
                        # Default to 0 for test_idx and 1 for step_idx if conversion fails
                        if not isinstance(test_idx, int):
                            test_idx = 0
                        if not isinstance(step_idx, int):
                            # Start from 1 instead of 0 to match UI display
                            step_idx = 1

                    # Get the current screenshots directory
                    from app.app import current_screenshots_dir
                    if current_screenshots_dir and os.path.exists(current_screenshots_dir):
                        # Get action_id from the action if it exists
                        action_id = action.get('action_id')
                        if not action_id:
                            # We should never generate a new action_id here
                            # Log a warning and use a placeholder action_id
                            self.logger.warning("No action_id provided for screenshot - this should not happen")
                            # Use a placeholder that's clearly not a real action_id
                            action_id = "placeholder"
                            action['action_id'] = action_id
                            self.logger.warning(f"Using placeholder action_id for screenshot: {action_id}")

                        # Create a standardized filename using action_id
                        standardized_filename = f"{action_id}.png"
                        self.logger.info(f"Using action_id for screenshot filename: {standardized_filename}")

                        # Create the full path for the screenshot - SAVE DIRECTLY TO REPORT FOLDER
                        # First, get both app screenshots dir and report screenshots dir
                        from app.app import current_screenshots_dir

                        # Create the full path for the screenshot in the report folder
                        screenshot_path_full = os.path.join(current_screenshots_dir, standardized_filename)
                        self.logger.info(f"Saving screenshot directly to report folder: {screenshot_path_full}")

                        # Take the screenshot with the exact filename, suite_id, and action_id
                        suite_id = getattr(self, 'suite_id', None)

                        # Check if a screenshot with this action_id already exists
                        from app.utils.database import check_screenshot_exists
                        if check_screenshot_exists(action_id):
                            self.logger.info(f"Screenshot with action_id {action_id} already exists, skipping screenshot")
                            # Use the existing screenshot path
                            screenshot_path = os.path.join(current_screenshots_dir, standardized_filename)

                            # Check if the file exists in the report folder
                            if not os.path.exists(screenshot_path):
                                # If not in report folder, check if it exists in static folder
                                static_path = os.path.join(self.device_controller.screenshot_dir, standardized_filename)
                                if os.path.exists(static_path):
                                    # Copy from static folder to report folder
                                    import shutil
                                    self.logger.info(f"Copying existing screenshot from static to report folder: {static_path} -> {screenshot_path}")
                                    shutil.copy2(static_path, screenshot_path)
                                else:
                                    # If not found anywhere, take a new screenshot but use the same action_id
                                    self.logger.info(f"Screenshot with action_id {action_id} exists in database but file not found, taking new screenshot")
                                    screenshot_result = self.device_controller.take_screenshot(
                                        filename=screenshot_path_full,
                                        test_idx=test_idx,
                                        step_idx=step_idx,
                                        suite_id=suite_id,
                                        action_id=action_id
                                    )

                                    # Set the screenshot_path for return value
                                    if isinstance(screenshot_result, dict) and screenshot_result.get('status') == 'success':
                                        screenshot_path = screenshot_result.get('path')
                                    else:
                                        screenshot_path = screenshot_path_full
                        else:
                            # Take a new screenshot
                            screenshot_result = self.device_controller.take_screenshot(
                                filename=screenshot_path_full,
                                test_idx=test_idx,
                                step_idx=step_idx,
                                suite_id=suite_id,
                                action_id=action_id
                            )

                            # Set the screenshot_path for return value
                            if isinstance(screenshot_result, dict) and screenshot_result.get('status') == 'success':
                                screenshot_path = screenshot_result.get('path')
                            else:
                                screenshot_path = screenshot_path_full
                    else:
                        # Fall back to default behavior if no screenshots directory
                        self.logger.warning("No screenshots directory set, using default screenshot behavior")
                        suite_id = getattr(self, 'suite_id', None)
                        # Get action_id from the action if it exists
                        action_id = action.get('action_id')
                        if not action_id:
                            # We should never generate a new action_id here
                            # Log a warning and use a placeholder action_id
                            self.logger.warning("No action_id provided for screenshot - this should not happen")
                            # Use a placeholder that's clearly not a real action_id
                            action_id = "placeholder"
                            action['action_id'] = action_id
                            self.logger.warning(f"Using placeholder action_id for screenshot: {action_id}")
                        self.logger.info(f"Using action_id for screenshot: {action_id}")

                        # Check if a screenshot with this action_id already exists
                        from app.utils.database import check_screenshot_exists
                        if check_screenshot_exists(action_id):
                            self.logger.info(f"Screenshot with action_id {action_id} already exists, skipping screenshot")
                            # Use the existing screenshot path
                            screenshot_path = os.path.join(self.device_controller.screenshot_dir, f"{action_id}.png")

                            # Check if the file actually exists
                            if not os.path.exists(screenshot_path):
                                # If not found, take a new screenshot but use the same action_id
                                self.logger.info(f"Screenshot with action_id {action_id} exists in database but file not found, taking new screenshot")
                                screenshot_result = self.device_controller.take_screenshot(
                                    test_idx=test_idx,
                                    step_idx=step_idx,
                                    suite_id=suite_id,
                                    action_id=action_id
                                )

                                if isinstance(screenshot_result, dict) and screenshot_result.get('path'):
                                    screenshot_path = screenshot_result.get('path')
                                else:
                                    screenshot_path = os.path.join(self.device_controller.screenshot_dir, f"{action_id}.png")
                        else:
                            # Take a new screenshot
                            screenshot_result = self.device_controller.take_screenshot(
                                test_idx=test_idx,
                                step_idx=step_idx,
                                suite_id=suite_id,
                                action_id=action_id
                            )

                            if isinstance(screenshot_result, dict) and screenshot_result.get('path'):
                                screenshot_path = screenshot_result.get('path')
                            else:
                                screenshot_path = os.path.join(self.device_controller.screenshot_dir, f"{action_id}.png")
                else:
                    self.logger.info("Skipping screenshot for non-addLog action to improve performance")
            except Exception as screenshot_err:
                self.logger.warning(f"Could not take screenshot after action: {screenshot_err}")

            # Track execution completion in the database
            try:
                # Import the database tracking function
                from app.utils.database import track_test_execution

                # Import the current test and step indices from app.py
                from app.app import current_test_idx, current_step_idx, current_suite_id

                # Get the test case filename if available
                filename = action.get('filename', 'unknown')

                # Get the action type
                action_type = action.get('type', 'unknown')

                # Determine success status
                if isinstance(result, dict):
                    success = result.get('status') == 'success'
                    message = result.get('message', '')
                elif isinstance(result, tuple) and len(result) >= 2:
                    success = result[0]
                    message = result[1]
                else:
                    success = bool(result)
                    message = "Unknown result format"

                # First check if test_idx is provided in the action
                action_test_idx = action.get('test_idx')
                if action_test_idx is not None:
                    local_test_idx = action_test_idx
                    self.logger.info(f"DEBUG: Using test_idx from action: {local_test_idx} for tracking completion")
                    # Also update our internal test_idx to match
                    self.current_test_idx = local_test_idx
                    self.logger.info(f"DEBUG: Updated player's current_test_idx to match action: {self.current_test_idx}")
                    # Also update the global current_test_idx
                    current_test_idx.value = local_test_idx
                    self.logger.info(f"DEBUG: Updated global current_test_idx.value to match action: {local_test_idx}")
                else:
                    # Use the locally stored test_idx if available, otherwise use current_test_idx.value
                    local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                    self.logger.info(f"DEBUG: Using local_test_idx: {local_test_idx} for tracking completion")

                self.logger.info(f"DEBUG: Player instance ID: {id(self)}, has current_test_idx: {hasattr(self, 'current_test_idx')}")
                if hasattr(self, 'current_test_idx'):
                    self.logger.info(f"DEBUG: Player's current_test_idx value: {self.current_test_idx}")
                self.logger.info(f"DEBUG: Global current_test_idx.value: {current_test_idx.value}")

                # Add a very distinctive log message to help debug test_idx issues
                self.logger.info(f"========== PLAYER COMPLETING ACTION WITH TEST_IDX: {local_test_idx} ==========")
                self.logger.info(f"========== ACTION TYPE: {action.get('type', 'Unknown')} ==========")
                self.logger.info(f"========== GLOBAL CURRENT_TEST_IDX: {current_test_idx.value} ==========")
                self.logger.info(f"========== PLAYER CURRENT_TEST_IDX: {getattr(self, 'current_test_idx', 'Not Set')} ==========")

                # Track the execution completion with action details
                track_test_execution(
                    suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                    test_idx=local_test_idx,
                    step_idx=current_step_idx.value,
                    filename=filename,
                    status='passed' if success else 'failed',
                    in_progress=False,
                    action_type=action_type,
                    action_params=action,
                    error=None if success else message
                )

                self.logger.info(f"Tracked execution completion in database: test_idx={local_test_idx}, step_idx={current_step_idx.value}, action_type={action_type}, status={'passed' if success else 'failed'}")
            except Exception as e:
                self.logger.error(f"Error tracking execution completion in database: {str(e)}")

            # Determine success status for database tracking
            if isinstance(result, dict):
                if result.get('status') != 'success':
                    error_message = result.get('message', '')
                    self.logger.info(f"Action failed with error: {error_message}")
            elif isinstance(result, tuple) and len(result) >= 2:
                success, message = result[0], result[1]
                if not success:
                    self.logger.info(f"Action failed with error: {message}")
            elif not result:
                self.logger.info("Action failed with unknown error")

            # Return results
            if isinstance(result, dict):
                # For dictionary results, convert to tuple format for consistency
                success = result.get('status') == 'success'
                message = result.get('message', '')
                return success, message, screenshot_path
            else:
                return result

        except Exception as e:
            self.logger.error(f"Error executing action: {e}")
            import traceback
            traceback.print_exc()

            # Log the error but don't store it in last_error
            self.logger.info(f"Action failed with exception: {str(e)}")

            # Track execution failure in the database
            try:
                # Import the database tracking function
                from app.utils.database import track_test_execution

                # Import the current test and step indices from app.py
                from app.app import current_test_idx, current_step_idx, current_suite_id

                # Get the test case filename if available
                filename = action.get('filename', 'unknown')

                # Get the action type
                action_type = action.get('type', 'unknown')

                # First check if test_idx is provided in the action
                action_test_idx = action.get('test_idx')
                if action_test_idx is not None:
                    local_test_idx = action_test_idx
                    self.logger.info(f"DEBUG: Using test_idx from action: {local_test_idx} for tracking failure")
                    # Also update our internal test_idx to match
                    self.current_test_idx = local_test_idx
                    self.logger.info(f"DEBUG: Updated player's current_test_idx to match action: {self.current_test_idx}")
                    # Also update the global current_test_idx
                    current_test_idx.value = local_test_idx
                    self.logger.info(f"DEBUG: Updated global current_test_idx.value to match action: {local_test_idx}")
                else:
                    # Use the locally stored test_idx if available, otherwise use current_test_idx.value
                    local_test_idx = getattr(self, 'current_test_idx', current_test_idx.value)
                    self.logger.info(f"DEBUG: Using local_test_idx: {local_test_idx} for tracking failure")

                self.logger.info(f"DEBUG: Player instance ID: {id(self)}, has current_test_idx: {hasattr(self, 'current_test_idx')}")
                if hasattr(self, 'current_test_idx'):
                    self.logger.info(f"DEBUG: Player's current_test_idx value: {self.current_test_idx}")
                self.logger.info(f"DEBUG: Global current_test_idx.value: {current_test_idx.value}")

                # Add a very distinctive log message to help debug test_idx issues
                self.logger.info(f"========== PLAYER HANDLING ERROR WITH TEST_IDX: {local_test_idx} ==========")
                self.logger.info(f"========== ACTION TYPE: {action.get('type', 'Unknown')} ==========")
                self.logger.info(f"========== ERROR: {str(e)} ==========")
                self.logger.info(f"========== GLOBAL CURRENT_TEST_IDX: {current_test_idx.value} ==========")
                self.logger.info(f"========== PLAYER CURRENT_TEST_IDX: {getattr(self, 'current_test_idx', 'Not Set')} ==========")

                # Track the execution failure with action details
                track_test_execution(
                    suite_id=getattr(self, 'suite_id', current_suite_id) or 'unknown',
                    test_idx=local_test_idx,
                    step_idx=current_step_idx.value,
                    filename=filename,
                    status='failed',
                    in_progress=False,
                    action_type=action_type,
                    action_params=action,
                    error=str(e)
                )

                self.logger.info(f"Tracked execution failure in database: test_idx={local_test_idx}, step_idx={current_step_idx.value}, action_type={action_type}, error={str(e)}")
            except Exception as db_error:
                self.logger.error(f"Error tracking execution failure in database: {str(db_error)}")

            return False, f"Action execution failed: {str(e)}", None

    def play_with_validation(self, actions=None):
        """Play back with visual validation using OpenCV"""
        if actions:
            self.actions = actions

        if not self.actions:
            print("No actions to play")
            return False

        import cv2
        import numpy as np

        self.is_playing = True

        # Skip initial state if present
        start_index = 0
        if self.actions and self.actions[0]['type'] == 'initial_state':
            start_index = 1

        # Play each action with validation
        last_timestamp = 0
        for i in range(start_index, len(self.actions)):
            if not self.is_playing:
                break

            action = self.actions[i]

            # Wait for appropriate time between actions
            current_timestamp = action['timestamp']
            wait_time = current_timestamp - last_timestamp

            if wait_time > 0:
                time.sleep(wait_time)

            # Execute the action
            self.execute_action(action)

            # Validate visual state if we have a screenshot reference
            if 'screenshot' in action:
                self._validate_visual_state(action)

            # Update state
            self.current_action_index = i
            last_timestamp = current_timestamp

        self.is_playing = False
        return True

    def _validate_visual_state(self, action):
        """Validate the visual state matches what we expect"""
        try:
            # Get reference screenshot path
            ref_screenshot = action.get('screenshot')
            if not ref_screenshot:
                return False

            # Make the path absolute
            abs_ref_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                'app', ref_screenshot
            )

            # Take a current screenshot
            # Get action_id from the action if it exists
            action_id = action.get('action_id')
            if action_id:
                self.logger.info(f"Using action_id from action for screenshot: {action_id}")

                # Check if a screenshot with this action_id already exists
                from app.utils.database import check_screenshot_exists
                if check_screenshot_exists(action_id):
                    self.logger.info(f"Screenshot with action_id {action_id} already exists, using existing screenshot")

                    # Try to find the existing screenshot file
                    try:
                        # First check in the current screenshots directory if available
                        from app.app import current_screenshots_dir
                        if current_screenshots_dir and os.path.exists(current_screenshots_dir):
                            existing_path = os.path.join(current_screenshots_dir, f"{action_id}.png")
                            if os.path.exists(existing_path):
                                self.logger.info(f"Found existing screenshot in current report folder: {existing_path}")
                                current_screenshot = existing_path
                            else:
                                # If not in report folder, check if it exists in static folder
                                static_path = os.path.join(self.device_controller.screenshot_dir, f"{action_id}.png")
                                if os.path.exists(static_path):
                                    # Copy from static folder to report folder
                                    import shutil
                                    self.logger.info(f"Copying existing screenshot from static to report folder: {static_path} -> {existing_path}")
                                    shutil.copy2(static_path, existing_path)
                                    current_screenshot = existing_path
                                else:
                                    # If not found anywhere, take a new screenshot but use the same action_id
                                    self.logger.info(f"Screenshot with action_id {action_id} exists in database but file not found, taking new screenshot")
                                    current_screenshot = self.device_controller.take_screenshot(action_id=action_id)
                        else:
                            # If no report directory, check static directory
                            static_path = os.path.join(self.device_controller.screenshot_dir, f"{action_id}.png")
                            if os.path.exists(static_path):
                                self.logger.info(f"Found existing screenshot in static folder: {static_path}")
                                current_screenshot = static_path
                            else:
                                # If not found anywhere, take a new screenshot but use the same action_id
                                self.logger.info(f"Screenshot with action_id {action_id} exists in database but file not found, taking new screenshot")
                                current_screenshot = self.device_controller.take_screenshot(action_id=action_id)
                    except (ImportError, AttributeError, Exception) as e:
                        self.logger.warning(f"Error checking for existing screenshot: {e}")
                        # Fall back to static directory
                        static_path = os.path.join(self.device_controller.screenshot_dir, f"{action_id}.png")
                        if os.path.exists(static_path):
                            self.logger.info(f"Found existing screenshot in static folder: {static_path}")
                            current_screenshot = static_path
                        else:
                            # If not found anywhere, take a new screenshot but use the same action_id
                            self.logger.info(f"Screenshot with action_id {action_id} exists in database but file not found, taking new screenshot")
                            current_screenshot = self.device_controller.take_screenshot(action_id=action_id)
                else:
                    # Take a new screenshot
                    current_screenshot = self.device_controller.take_screenshot(action_id=action_id)
            else:
                # No action_id, take a new screenshot
                current_screenshot = self.device_controller.take_screenshot()

            # If current_screenshot is a dict (from take_screenshot), extract the path
            if isinstance(current_screenshot, dict) and 'path' in current_screenshot:
                current_screenshot = current_screenshot['path']

            # Make sure we have an absolute path
            if not os.path.isabs(current_screenshot):
                abs_current_path = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                    'app', current_screenshot
                )
            else:
                abs_current_path = current_screenshot

            # Give a short delay for the screen to update
            time.sleep(0.5)

            # Use OpenCV to compare images
            import cv2
            import numpy as np

            # Read images
            img1 = cv2.imread(abs_ref_path)
            img2 = cv2.imread(abs_current_path)

            # Convert to grayscale
            gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

            # Calculate structural similarity
            from skimage.metrics import structural_similarity
            score, _ = structural_similarity(gray1, gray2, full=True)

            # Print validation result
            print(f"Visual validation score: {score:.2f}")

            # Return whether it's similar enough
            return score > 0.8
        except Exception as e:
            print(f"Error validating visual state: {e}")
            return False

    def execute_all_actions(self, actions):
        """Execute a list of actions in sequence"""
        self.stop_flag = False
        self.current_action_index = -1
        self.total_actions = len(actions)
        success = True  # Track overall success
        error_message = None  # Track error message for failed actions

        self.logger.info(f"Starting execution of {self.total_actions} actions")

        # Import the current_test_idx and current_step_idx from app.py
        from app.app import current_test_idx, current_step_idx

        # Log the initial values
        self.logger.info(f"Initial test_idx={current_test_idx.value}, step_idx={current_step_idx.value}")

        # Make sure test_idx is set to 0 for single test case execution
        # This ensures consistent indexing between single test case and test suite execution
        current_test_idx.value = 0
        self.logger.info(f"Set test_idx to 0 for single test case execution")

        # Reset last_error at the beginning of execution
        # Reset error tracking at the beginning of execution
        self.last_error = None
        self.logger.info("Reset last_error at the beginning of execute_all_actions")

        for i, action in enumerate(actions):
            if self.stop_flag:
                self.logger.info("Execution stopped by user")
                success = False
                error_message = "Execution stopped by user"
                break

            self.current_action_index = i

            try:
                action_type = action.get('type', 'unknown')
                action_id = action.get('action_id', '')

                # Include action_id in the log message if available
                if action_id:
                    self.logger.info(f"Executing action {i+1}/{self.total_actions}: {action_type} (action_id: {action_id})")
                else:
                    self.logger.info(f"Executing action {i+1}/{self.total_actions}: {action_type}")

                # For sequential actions, use the current action index as step_idx
                # This ensures each action gets a unique step index
                # Start from 1 instead of 0 to match the UI display
                step_idx = i + 1

                # Use the current test_idx value (should be 0 for single test case)
                test_idx = current_test_idx.value

                # Special case for test case/suite execution - reset step index
                if action.get('type') == 'executeTestCase' or action.get('type') == 'executeTestSuite':
                    # Reset step index for new test case/suite
                    # Start from 1 instead of 0 to match the UI display
                    step_idx = 1
                    self.logger.info(f"Resetting step index for new test case/suite to 1")

                # Make sure we're using integers
                try:
                    test_idx = int(test_idx)
                    step_idx = int(step_idx)
                except (ValueError, TypeError):
                    self.logger.warning(f"Invalid test_idx or step_idx, using defaults. test_idx={test_idx}, step_idx={step_idx}")
                    test_idx = current_test_idx.value
                    # Start from 1 instead of 0 to match UI display
                    step_idx = i + 1

                # Update the global indices
                current_test_idx.value = test_idx
                current_step_idx.value = step_idx

                self.logger.info(f"=== UPDATING INDICES IN EXECUTE_ALL_ACTIONS ===")
                self.logger.info(f"Action: {action.get('type')}")
                self.logger.info(f"Action data: {action}")
                self.logger.info(f"Set indices for action {i}: test_idx={test_idx}, step_idx={step_idx}")

                # Wait for specified interval before executing next action
                if i > 0 and 'interval' in action:
                    interval = action.get('interval', 500)  # Default 500ms
                    time.sleep(interval / 1000)

                result = self.execute_action(action)

                # Handle both tuple and dictionary result formats
                if isinstance(result, tuple) and len(result) >= 2:
                    action_success, message = result[0], result[1]
                    if not action_success:
                        self.logger.error(f"Action {i+1} failed: {message}")
                        success = False
                        error_message = message
                        # Don't break, continue with next action
                        self.logger.info(f"Continuing execution with the next action despite failure")
                        continue
                    else:
                        self.logger.info(f"Action {i+1} completed successfully")
                elif isinstance(result, dict):
                    # Legacy dictionary format handling
                    if result.get("status") == "error":
                        self.logger.error(f"Action {i+1} failed: {result.get('message')}")
                        success = False
                        error_message = result.get('message')
                        # Don't break, continue with next action
                        self.logger.info(f"Continuing execution with the next action despite failure")
                        continue
                    else:
                        self.logger.info(f"Action {i+1} completed successfully")
                else:
                    # Treat as success if result is truthy
                    if result:
                        self.logger.info(f"Action {i+1} completed successfully")
                    else:
                        self.logger.error(f"Action {i+1} failed: Unknown error")
                        success = False
                        error_message = "Unknown error"
                        # Don't break, continue with next action
                        self.logger.info(f"Continuing execution with the next action despite failure")
                        continue

            except Exception as e:
                self.logger.error(f"Error in action {i+1}: {str(e)}")
                success = False
                error_message = str(e)
                # Don't break, continue with next action
                self.logger.info(f"Continuing execution with the next action despite exception: {str(e)}")
                continue

        self.logger.info("All actions executed")
        self.current_action_index = -1

        # Return success status and error message
        return success, error_message or "All actions executed successfully"

    def stop_execution(self):
        """Stop executing actions"""
        self.execution_stopped = True
        return True

    def _execute_tap(self, x, y):
        """Execute a tap action at the specified coordinates"""
        try:
            self.device_controller.tap(x, y)
            return {"status": "success", "message": f"Tapped at ({x}, {y})"}
        except Exception as e:
            return {"status": "error", "message": f"Failed to tap at ({x}, {y}): {str(e)}"}

    def _execute_swipe(self, start_x, start_y, end_x, end_y, duration):
        """Execute a swipe action between the specified coordinates"""
        try:
            self.device_controller.swipe(start_x, start_y, end_x, end_y, duration)
            return {"status": "success", "message": f"Swiped from ({start_x}, {start_y}) to ({end_x}, {end_y})"}
        except Exception as e:
            return {"status": "error", "message": f"Failed to swipe: {str(e)}"}

    def _execute_text(self, text):
        """Execute a text input action"""
        try:
            # Apply parameter substitution
            try:
                from app.utils.parameter_utils import substitute_parameters
                original_text = text
                substituted_text = substitute_parameters(original_text)

                # Log the substitution if it occurred
                if substituted_text != original_text:
                    self.logger.info(f"Parameter substitution in _execute_text: '{original_text}' -> '{substituted_text}'")
                    # Update the text with the substituted value
                    text = substituted_text
            except Exception as subst_error:
                self.logger.error(f"Error applying parameter substitution: {str(subst_error)}")
                # Continue with the original text

            # Check if we're on iOS platform
            is_ios = hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name.lower() == 'ios'

            if is_ios and hasattr(self.device_controller, 'driver') and self.device_controller.driver:
                self.logger.info(f"Using iOS-specific text input methods for: '{text}'")

                # Method 1: Try using XCUITest's typeText method via mobile: commands
                try:
                    self.logger.info(f"Method 1: Using XCUITest mobile:typeText to input text: '{text}'")

                    # Use the mobile: typeText command which is the most reliable for iOS
                    self.device_controller.driver.execute_script('mobile: typeText', {'text': text})

                    return {"status": "success", "message": f"Input text using XCUITest mobile:typeText: '{text}'"}
                except Exception as ios_e1:
                    self.logger.warning(f"Method 1 failed to input text using XCUITest mobile:typeText: {ios_e1}")

                    # Method 2: Try using active element
                    try:
                        self.logger.info(f"Method 2: Using active element to input text: '{text}'")

                        # Get the active element
                        active_element = self.device_controller.driver.switch_to.active_element

                        # Send keys to the active element
                        active_element.send_keys(text)

                        return {"status": "success", "message": f"Input text using active element: '{text}'"}
                    except Exception as ios_e2:
                        self.logger.warning(f"Method 2 failed to input text using active element: {ios_e2}")

                        # Method 3: Try using mobile: type (older method)
                        try:
                            self.logger.info(f"Method 3: Using mobile: type to input text: '{text}'")

                            # Use mobile: type command for iOS (older method)
                            self.device_controller.driver.execute_script('mobile: type', {'text': text})

                            return {"status": "success", "message": f"Input text using mobile: type: '{text}'"}
                        except Exception as ios_e3:
                            self.logger.warning(f"Method 3 failed to input text using mobile: type: {ios_e3}")
                            # Fall through to standard method

            # Standard method for all platforms
            self.logger.info(f"Using standard text input method for: '{text}'")
            result = self.device_controller.input_text(text)

            # Handle different return types
            if isinstance(result, dict):
                return result
            elif result is True:
                return {"status": "success", "message": f"Input text: '{text}'"}
            else:
                return {"status": "error", "message": f"Failed to input text: '{text}'"}
        except Exception as e:
            return {"status": "error", "message": f"Failed to input text: {str(e)}"}

    def _execute_key(self, key_code):
        """Execute a key press action"""
        try:
            self.device_controller.press_keycode(key_code)
            return {"status": "success", "message": f"Pressed key: {key_code}"}
        except Exception as e:
            return {"status": "error", "message": f"Failed to press key: {str(e)}"}

    def _execute_wait(self, duration):
        """Wait for the specified duration in seconds"""
        try:
            # Sleep for the specified duration
            time.sleep(duration)
            return {"status": "success", "message": f"Waited for {duration} seconds"}
        except Exception as e:
            return {"status": "error", "message": f"Failed to wait: {str(e)}"}

    def _execute_launch_app(self, package):
        """Launch application using Airtest start_app method"""
        try:
            # Check if device controller has AirTest device
            if hasattr(self.device_controller, 'airtest_device') and self.device_controller.airtest_device:
                try:
                    # Import AirTest API
                    from airtest.core.api import start_app

                    # Launch the app
                    start_app(package)
                    return {"status": "success", "message": f"Launched app {package}"}
                except Exception as air_error:
                    self.logger.warning(f"Airtest start_app failed: {str(air_error)}, falling back to ADB")
                    # Fall back to ADB command if Airtest fails
                    return self.device_controller.launch_app(package)
            else:
                # Fallback to ADB command through device controller
                result = self.device_controller.launch_app(package)
                if isinstance(result, dict):
                    return result
                elif result:
                    return {"status": "success", "message": f"Launched app {package}"}
                else:
                    return {"status": "error", "message": f"Failed to launch app using device controller"}
        except Exception as e:
            return {"status": "error", "message": f"Failed to launch app: {str(e)}"}

    def _execute_terminate_app(self, package):
        """Terminate application using Airtest stop_app method"""
        try:
            # Check if device controller has AirTest device
            if hasattr(self.device_controller, 'airtest_device') and self.device_controller.airtest_device:
                try:
                    # Import AirTest API
                    from airtest.core.api import stop_app

                    # Stop the app
                    stop_app(package)
                    return {"status": "success", "message": f"Terminated app {package}"}
                except Exception as air_error:
                    self.logger.warning(f"Airtest stop_app failed: {str(air_error)}, falling back to ADB")
                    # Fall back to ADB command if Airtest fails
                    return self.device_controller.terminate_app(package)
            else:
                # Fallback to ADB command through device controller
                result = self.device_controller.terminate_app(package)
                if isinstance(result, dict):
                    return result
                elif result:
                    return {"status": "success", "message": f"Terminated app {package}"}
                else:
                    return {"status": "error", "message": f"Failed to terminate app using device controller"}
        except Exception as e:
            return {"status": "error", "message": f"Failed to terminate app: {str(e)}"}

    def _execute_restart_app(self, package):
        """Restart application using Airtest clear_app and start_app methods"""
        try:
            # Check if device controller has AirTest device
            if hasattr(self.device_controller, 'airtest_device') and self.device_controller.airtest_device:
                try:
                    # Import AirTest API
                    from airtest.core.api import clear_app, start_app

                    # Clear the app data
                    clear_app(package)
                    # Start the app
                    start_app(package)
                    return {"status": "success", "message": f"Restarted app {package}"}
                except Exception as air_error:
                    self.logger.warning(f"Airtest restart failed: {str(air_error)}, falling back to ADB")
                    # Fallback to ADB commands through device_controller
                    stop_result = self.device_controller.terminate_app(package)
                    if isinstance(stop_result, dict) and stop_result.get("status") == "error":
                        return stop_result

                    time.sleep(1)  # Short delay to ensure app is fully stopped

                    launch_result = self.device_controller.launch_app(package)
                    return launch_result
            else:
                # Fallback to ADB commands
                stop_result = self.device_controller.terminate_app(package)
                if isinstance(stop_result, dict) and stop_result.get("status") == "error":
                    return stop_result

                time.sleep(1)  # Short delay to ensure app is fully stopped

                launch_result = self.device_controller.launch_app(package)
                if isinstance(launch_result, dict):
                    return launch_result
                elif launch_result:
                    return {"status": "success", "message": f"Restarted app {package}"}
                else:
                    return {"status": "error", "message": f"Failed to restart app using device controller"}
        except Exception as e:
            return {"status": "error", "message": f"Failed to restart app: {str(e)}"}

    def _execute_wait_till(self, image, timeout, interval):
        """Wait until image appears on screen"""
        try:
            # Check if image parameter is valid
            if not image:
                return {"status": "error", "message": "No image specified for wait_till action"}

            # Log the image parameter for debugging
            self.logger.info(f"Wait till image parameter: '{image}'")

            # Check if reference image exists first
            from pathlib import Path
            import os
            import sys

            # Get the project root directory (parent of app directory)
            project_root = Path(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            reference_dir = project_root / 'reference_images'

            # Make sure image has a file extension
            if not os.path.splitext(image)[1]:
                self.logger.warning(f"Image parameter '{image}' has no file extension, trying to add .png")
                image = f"{image}.png"

            # Try multiple paths to find the image
            potential_paths = [
                reference_dir / image,  # Standard path in reference_images
                Path(image),  # Direct path as provided
                Path('reference_images') / image,  # reference_images in current dir
                project_root / 'app' / 'reference_images' / image,  # app/reference_images
            ]

            # Try to use config.py for reference_images path
            try:
                sys.path.insert(0, str(project_root))
                import config
                if hasattr(config, 'DIRECTORIES') and 'REFERENCE_IMAGES' in config.DIRECTORIES:
                    config_ref_images = config.DIRECTORIES['REFERENCE_IMAGES']
                    if isinstance(config_ref_images, str) or hasattr(config_ref_images, '__fspath__'):
                        config_path = Path(str(config_ref_images)) / image
                        potential_paths.append(config_path)
                        self.logger.info(f"Added config.py path: {config_path}")
            except ImportError as e:
                self.logger.warning(f"Could not import config.py: {e}")
            except Exception as e:
                self.logger.warning(f"Error getting reference_images from config.py: {e}")

            # Log all paths being checked
            self.logger.info(f"Checking these image paths:")
            for idx, path in enumerate(potential_paths):
                exists = os.path.exists(path)
                status = "EXISTS" if exists else "NOT FOUND"
                self.logger.info(f"  [{idx+1}] {path} - {status}")

            # Find the first path that exists
            image_path = None
            for path in potential_paths:
                if os.path.exists(path):
                    image_path = path
                    self.logger.info(f"Found image at: {image_path}")
                    break

            # If no path exists, return error
            if not image_path:
                self.logger.error(f"Reference image not found in any location: {image}")
                return {"status": "error", "message": f"Reference image '{image}' not found in any location"}

            # Convert timeout and interval to seconds if needed
            timeout_sec = float(timeout)
            interval_sec = float(interval)

            self.logger.info(f"Waiting for image {image} with timeout={timeout_sec}s, interval={interval_sec}s")

            # Try multiple methods to find the image, starting with the most reliable

            # Method 1: Try using Airtest directly if available
            if hasattr(self.device_controller, 'airtest_device') and self.device_controller.airtest_device:
                try:
                    from airtest.core.api import Template, wait, exists

                    # Log the absolute path to help debugging
                    abs_path = os.path.abspath(str(image_path))
                    self.logger.info(f"Using absolute image path with Airtest: {abs_path}")

                    # Create template with appropriate threshold
                    template = Template(abs_path, threshold=0.8)
                    self.logger.info(f"Created Airtest Template object: {template}")

                    # Try to wait for the image with timeout
                    try:
                        self.logger.info(f"Waiting for image using Airtest wait() with timeout={timeout_sec}s")
                        match_pos = wait(template, timeout=timeout_sec)

                        if match_pos:
                            # Found the image, extract coordinates
                            center_x, center_y = match_pos
                            self.logger.info(f"Image found at: ({center_x}, {center_y}) using Airtest wait()")

                            return {
                                "status": "success",
                                "message": f"Image {image} found at ({center_x}, {center_y})",
                                "position": (center_x, center_y),
                                "confidence": 1.0  # Airtest doesn't return confidence
                            }
                    except Exception as wait_err:
                        self.logger.warning(f"Airtest wait() failed: {wait_err}, trying exists()")

                        # Try exists() as a fallback
                        match_pos = exists(template)
                        if match_pos:
                            # Found the image, extract coordinates
                            center_x, center_y = match_pos
                            self.logger.info(f"Image found at: ({center_x}, {center_y}) using Airtest exists()")

                            return {
                                "status": "success",
                                "message": f"Image {image} found at ({center_x}, {center_y})",
                                "position": (center_x, center_y),
                                "confidence": 1.0  # Airtest doesn't return confidence
                            }
                except ImportError as e:
                    self.logger.warning(f"Airtest API not available: {e}")
                except Exception as e:
                    self.logger.warning(f"Error using Airtest API directly: {e}")

            # Method 2: Try using the device controller's find_image method if available
            if hasattr(self.device_controller, 'find_image'):
                try:
                    self.logger.info(f"Trying device controller's find_image method")

                    # Start time for timeout tracking
                    start_time = time.time()

                    # Try until timeout
                    while time.time() - start_time < timeout_sec:
                        match_pos = self.device_controller.find_image(
                            str(image_path),
                            threshold=0.8,
                            timeout=min(5, timeout_sec - (time.time() - start_time))  # Use shorter timeout for each attempt
                        )

                        if match_pos:
                            # Found the image, extract coordinates
                            center_x, center_y = match_pos
                            self.logger.info(f"Image found at: ({center_x}, {center_y}) using device controller's find_image")

                            return {
                                "status": "success",
                                "message": f"Image {image} found at ({center_x}, {center_y})",
                                "position": (center_x, center_y),
                                "confidence": 1.0  # find_image doesn't return confidence
                            }

                        # Wait before next attempt
                        time.sleep(interval_sec)

                    self.logger.info(f"Image not found using device controller's find_image within timeout")
                except Exception as e:
                    self.logger.warning(f"Error using device controller's find_image: {e}")

            # Method 3: Use the image matcher from the device controller if available
            if hasattr(self.device_controller, 'image_matcher') and self.device_controller.image_matcher:
                try:
                    self.logger.info("Using image matcher from device controller")
                    matcher = self.device_controller.image_matcher

                    # Wait for the template to appear - use a lower threshold to match tap action behavior
                    # Tap action uses 0.5 threshold for OpenCV matching
                    found, position, confidence = matcher.wait_for_template(
                        str(image_path),
                        timeout=timeout_sec,
                        interval=interval_sec,
                        threshold=0.7  # Lower threshold to match tap action behavior
                    )

                    if found:
                        self.logger.info(f"Image found at position: {position} with confidence: {confidence:.4f}")
                        return {
                            "status": "success",
                            "message": f"Image {image} found at {position}",
                            "position": position,
                            "confidence": confidence
                        }
                    else:
                        self.logger.info(f"Image not found using device controller's image_matcher")
                except Exception as e:
                    self.logger.warning(f"Error using device controller's image_matcher: {e}")

            # Method 4: Create a new image matcher as a last resort
            try:
                self.logger.info("Creating new image matcher")
                from app.utils.image_matcher import ImageMatcher

                # Make sure we have a device ID
                device_id = None
                platform = None
                airtest_device = None

                if hasattr(self.device_controller, 'device_id'):
                    device_id = self.device_controller.device_id

                if hasattr(self.device_controller, 'platform_name'):
                    platform = self.device_controller.platform_name

                if hasattr(self.device_controller, 'airtest_device'):
                    airtest_device = self.device_controller.airtest_device

                if not device_id:
                    return {"status": "error", "message": "No device ID available for image matching"}

                # Create the image matcher and set device ID, platform, and Airtest device
                matcher = ImageMatcher(device_id, platform=platform)

                # Set the Airtest device if available (needed for iOS screenshots)
                if airtest_device:
                    matcher.set_airtest_device(airtest_device)

                # Wait for the template to appear - use a lower threshold to match tap action behavior
                found, position, confidence = matcher.wait_for_template(
                    str(image_path),
                    timeout=timeout_sec,
                    interval=interval_sec,
                    threshold=0.7  # Lower threshold to match tap action behavior
                )

                if found:
                    self.logger.info(f"Image found at position: {position} with confidence: {confidence:.4f}")
                    return {
                        "status": "success",
                        "message": f"Image {image} found at {position}",
                        "position": position,
                        "confidence": confidence
                    }
            except Exception as e:
                self.logger.error(f"Error creating and using new image matcher: {e}")

            # If we get here, all methods failed
            self.logger.info(f"Image {image} not found within timeout of {timeout_sec} seconds using all methods")
            return {
                "status": "error",
                "message": f"Image {image} not found within timeout of {timeout_sec} seconds using all available methods",
                "confidence": 0.0
            }
        except Exception as e:
            self.logger.error(f"Failed to execute wait_till: {str(e)}")
            return {"status": "error", "message": f"Failed to execute wait_till: {str(e)}"}

    def _execute_exists_image(self, image):
        """Check if image exists on screen"""
        try:
            # Check if image parameter is valid
            if not image:
                return {"status": "error", "message": "No image specified for exists_image action"}

            # Log the image parameter for debugging
            self.logger.info(f"Exists image parameter: '{image}'")

            # Check if reference image exists first
            from pathlib import Path
            import os
            import sys

            # Get the project root directory (parent of app directory)
            project_root = Path(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            reference_dir = project_root / 'reference_images'

            # Make sure image has a file extension
            if not os.path.splitext(image)[1]:
                self.logger.warning(f"Image parameter '{image}' has no file extension, trying to add .png")
                image = f"{image}.png"

            # Try multiple paths to find the image
            potential_paths = [
                reference_dir / image,  # Standard path in reference_images
                Path(image),  # Direct path as provided
                Path('reference_images') / image,  # reference_images in current dir
                project_root / 'app' / 'reference_images' / image,  # app/reference_images
            ]

            # Try to use config.py for reference_images path
            try:
                sys.path.insert(0, str(project_root))
                import config
                if hasattr(config, 'DIRECTORIES') and 'REFERENCE_IMAGES' in config.DIRECTORIES:
                    config_ref_images = config.DIRECTORIES['REFERENCE_IMAGES']
                    if isinstance(config_ref_images, str) or hasattr(config_ref_images, '__fspath__'):
                        config_path = Path(str(config_ref_images)) / image
                        potential_paths.append(config_path)
                        self.logger.info(f"Added config.py path: {config_path}")
            except ImportError as e:
                self.logger.warning(f"Could not import config.py: {e}")
            except Exception as e:
                self.logger.warning(f"Error getting reference_images from config.py: {e}")

            # Log all paths being checked
            self.logger.info(f"Checking these image paths:")
            for idx, path in enumerate(potential_paths):
                exists = os.path.exists(path)
                status = "EXISTS" if exists else "NOT FOUND"
                self.logger.info(f"  [{idx+1}] {path} - {status}")

            # Find the first path that exists
            image_path = None
            for path in potential_paths:
                if os.path.exists(path):
                    image_path = path
                    self.logger.info(f"Found image at: {image_path}")
                    break

            # If no path exists, return error
            if not image_path:
                self.logger.error(f"Reference image not found in any location: {image}")
                return {"status": "error", "message": f"Reference image '{image}' not found in any location"}

            # Try multiple methods to check if the image exists on screen

            # Method 1: Try using Airtest directly if available
            if hasattr(self.device_controller, 'airtest_device') and self.device_controller.airtest_device:
                try:
                    from airtest.core.api import Template, exists

                    # Log the absolute path to help debugging
                    abs_path = os.path.abspath(str(image_path))
                    self.logger.info(f"Using absolute image path with Airtest: {abs_path}")

                    # Create template with appropriate threshold
                    template = Template(abs_path, threshold=0.8)
                    self.logger.info(f"Created Airtest Template object: {template}")

                    # Try to find the image
                    match_pos = exists(template)

                    if match_pos:
                        # Found the image, extract coordinates
                        center_x, center_y = match_pos
                        self.logger.info(f"Image found at: ({center_x}, {center_y}) using Airtest exists()")

                        return {
                            "status": "success",
                            "message": f"Image {image} found at ({center_x}, {center_y})",
                            "position": (center_x, center_y),
                            "confidence": 1.0,  # Airtest doesn't return confidence
                            "found": True
                        }
                    else:
                        self.logger.info(f"Image not found using Airtest exists()")
                except ImportError as e:
                    self.logger.warning(f"Airtest API not available: {e}")
                except Exception as e:
                    self.logger.warning(f"Error using Airtest API directly: {e}")

            # Method 2: Try using the device controller's find_image method if available
            if hasattr(self.device_controller, 'find_image'):
                try:
                    self.logger.info(f"Trying device controller's find_image method")

                    # Use a short timeout since this is just a check
                    match_pos = self.device_controller.find_image(
                        str(image_path),
                        threshold=0.8,
                        timeout=2  # Short timeout for exists check
                    )

                    if match_pos:
                        # Found the image, extract coordinates
                        center_x, center_y = match_pos
                        self.logger.info(f"Image found at: ({center_x}, {center_y}) using device controller's find_image")

                        return {
                            "status": "success",
                            "message": f"Image {image} found at ({center_x}, {center_y})",
                            "position": (center_x, center_y),
                            "confidence": 1.0,  # find_image doesn't return confidence
                            "found": True
                        }
                    else:
                        self.logger.info(f"Image not found using device controller's find_image")
                except Exception as e:
                    self.logger.warning(f"Error using device controller's find_image: {e}")

            # Method 3: Use the image matcher from the device controller if available
            if hasattr(self.device_controller, 'image_matcher') and self.device_controller.image_matcher:
                try:
                    self.logger.info("Using image matcher from device controller")
                    matcher = self.device_controller.image_matcher

                    # Check if the image exists
                    found, position, confidence = matcher.find_template(
                        str(image_path),
                        threshold=0.8
                    )

                    if found:
                        self.logger.info(f"Image found at position: {position} with confidence: {confidence:.4f}")
                        return {
                            "status": "success",
                            "message": f"Image {image} found at {position}",
                            "position": position,
                            "confidence": confidence,
                            "found": True
                        }
                    else:
                        self.logger.info(f"Image not found using device controller's image_matcher (best confidence: {confidence:.4f})")
                except Exception as e:
                    self.logger.warning(f"Error using device controller's image_matcher: {e}")

            # Method 4: Create a new image matcher as a last resort
            try:
                self.logger.info("Creating new image matcher")
                from app.utils.image_matcher import ImageMatcher

                # Make sure we have a device ID
                device_id = None
                platform = None
                airtest_device = None

                if hasattr(self.device_controller, 'device_id'):
                    device_id = self.device_controller.device_id

                if hasattr(self.device_controller, 'platform_name'):
                    platform = self.device_controller.platform_name

                if hasattr(self.device_controller, 'airtest_device'):
                    airtest_device = self.device_controller.airtest_device

                if not device_id:
                    return {"status": "error", "message": "No device ID available for image matching"}

                # Create the image matcher and set device ID, platform, and Airtest device
                matcher = ImageMatcher(device_id, platform=platform)

                # Set the Airtest device if available (needed for iOS screenshots)
                if airtest_device:
                    matcher.set_airtest_device(airtest_device)

                # Check if the image exists - use a lower threshold to match tap action behavior
                found, position, confidence = matcher.find_template(
                    str(image_path),
                    threshold=0.7  # Lower threshold to match tap action behavior
                )

                if found:
                    self.logger.info(f"Image found at position: {position} with confidence: {confidence:.4f}")
                    return {
                        "status": "success",
                        "message": f"Image {image} found at {position}",
                        "position": position,
                        "confidence": confidence,
                        "found": True
                    }
                else:
                    self.logger.info(f"Image not found using new image matcher (best confidence: {confidence:.4f})")
            except Exception as e:
                self.logger.error(f"Error creating and using new image matcher: {e}")

            # If we get here, all methods failed to find the image
            self.logger.info(f"Image {image} not found on screen using all methods")
            return {
                "status": "success",  # This is still a success response, just with found=False
                "message": f"Image {image} not found on screen using all available methods",
                "confidence": 0.0,
                "found": False
            }
        except Exception as e:
            self.logger.error(f"Failed to execute exists: {str(e)}")
            return {"status": "error", "message": f"Failed to execute exists: {str(e)}"}

    def _execute_exists_element(self, locator_type, locator_value, timeout):
        """Check if element exists on screen using locator type and value"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException, NoSuchElementException

            # Make sure we have a driver in device controller
            if not hasattr(self.device_controller, 'driver') or not self.device_controller.driver:
                return {"status": "error", "message": "No Appium driver available for element checking"}

            driver = self.device_controller.driver

            # Set up locator strategy
            locator = None
            if locator_type == 'id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ID, locator_value)
                self.logger.info(f"Checking for element with ID: {locator_value}")
            elif locator_type == 'xpath':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.XPATH, locator_value)
                self.logger.info(f"Checking for element with XPath: {locator_value}")
            elif locator_type == 'accessibility_id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ACCESSIBILITY_ID, locator_value)
                self.logger.info(f"Checking for element with Accessibility ID: {locator_value}")
            elif locator_type == 'class':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.CLASS_NAME, locator_value)
                self.logger.info(f"Checking for element with Class: {locator_value}")
            elif locator_type == 'name':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.NAME, locator_value)
                self.logger.info(f"Checking for element with Name: {locator_value}")
            elif locator_type == 'text':
                from appium.webdriver.common.appiumby import AppiumBy
                # Create an XPath that searches for text
                xpath = f"//*[@text='{locator_value}' or contains(@content-desc, '{locator_value}')]"
                locator = (AppiumBy.XPATH, xpath)
                self.logger.info(f"Checking for element with text: {locator_value}")
            else:
                return {"status": "error", "message": f"Unsupported locator type: {locator_type}"}

            try:
                # Try to find the element with the specified timeout
                element = WebDriverWait(driver, timeout).until(
                    EC.presence_of_element_located(locator)
                )

                # Element found
                element_text = None
                try:
                    element_text = element.text
                except:
                    pass

                self.logger.info(f"Element found: {locator_type}='{locator_value}'" +
                                 (f" with text '{element_text}'" if element_text else ""))

                return {
                    "status": "success",
                    "message": f"Element found: {locator_type}='{locator_value}'",
                    "found": True,
                    "element_text": element_text
                }
            except TimeoutException:
                # Element not found within timeout
                self.logger.info(f"Element not found: {locator_type}='{locator_value}' within {timeout} seconds")
                return {
                    "status": "success",
                    "message": f"Element not found: {locator_type}='{locator_value}'",
                    "found": False
                }
            except Exception as find_err:
                self.logger.error(f"Error finding element: {str(find_err)}")
                return {
                    "status": "error",
                    "message": f"Error checking for element: {str(find_err)}"
                }
        except Exception as e:
            self.logger.error(f"Failed to execute exists check: {str(e)}")
            return {"status": "error", "message": f"Failed to execute exists check: {str(e)}"}

    def _execute_double_click(self, x, y):
        """Perform double click at specified coordinates using Airtest double_click method"""
        try:
            # Check if device controller has AirTest device
            if hasattr(self.device_controller, 'airtest_device') and self.device_controller.airtest_device:
                try:
                    # Import AirTest API
                    from airtest.core.api import double_click

                    # Perform double click
                    double_click((x, y))
                    return {"status": "success", "message": f"Double clicked at ({x}, {y})"}
                except Exception as air_error:
                    self.logger.warning(f"Airtest double_click failed: {str(air_error)}, falling back to consecutive taps")
                    # Fall back to consecutive taps if Airtest fails
                    return self._fallback_double_tap(x, y)
            else:
                self.logger.info("No Airtest device available, using fallback double tap method")
                return self._fallback_double_tap(x, y)
        except Exception as e:
            return {"status": "error", "message": f"Failed to execute double click: {str(e)}"}

    def _fallback_double_tap(self, x, y):
        """Fallback method for double click using two consecutive taps"""
        try:
            self.logger.info(f"Using fallback double tap at ({x}, {y})")
            # First tap
            tap_result1 = self._execute_tap(x, y)
            if tap_result1.get("status") == "error":
                return tap_result1

            # Short delay between taps (100ms)
            time.sleep(0.1)

            # Second tap
            tap_result2 = self._execute_tap(x, y)
            if tap_result2.get("status") == "success":
                return {"status": "success", "message": f"Double clicked at ({x}, {y}) using consecutive taps"}
            else:
                return tap_result2
        except Exception as e:
            return {"status": "error", "message": f"Failed to execute fallback double tap: {str(e)}"}

    def _execute_wait_till_element(self, locator_type, locator_value, timeout, interval):
        """Wait until element appears on screen using various locator strategies"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException, NoSuchElementException

            # Make sure we have a driver in device controller
            if not hasattr(self.device_controller, 'driver') or not self.device_controller.driver:
                return {"status": "error", "message": "No Appium driver available for element waiting"}

            driver = self.device_controller.driver

            # Set up locator strategy
            locator = None
            if locator_type == 'id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ID, locator_value)
                self.logger.info(f"Waiting for element with ID: {locator_value}")
            elif locator_type == 'xpath':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.XPATH, locator_value)
                self.logger.info(f"Waiting for element with XPath: {locator_value}")
            elif locator_type == 'accessibility_id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ACCESSIBILITY_ID, locator_value)
                self.logger.info(f"Waiting for element with Accessibility ID: {locator_value}")
            elif locator_type == 'text':
                from appium.webdriver.common.appiumby import AppiumBy
                # Create an XPath that searches for text
                xpath = f"//*[@text='{locator_value}' or contains(@content-desc, '{locator_value}')]"
                locator = (AppiumBy.XPATH, xpath)
                self.logger.info(f"Waiting for element with text: {locator_value}")
            elif locator_type == 'image':
                # Handle image locator type using Airtest
                try:
                    import airtest.core.api as airtest_api
                    from airtest.core.cv import Template
                    self.logger.info(f"Waiting for image: {locator_value}")

                    # Convert to a proper image path if not already
                    image_path = locator_value
                    if not os.path.isabs(image_path):
                        # First check if file exists directly
                        if os.path.exists(image_path):
                            self.logger.info(f"Found image at relative path: {image_path}")
                        else:
                            # Build a list of paths to check
                            potential_paths = [
                                image_path,  # Current directory
                                os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                               'app', 'reference_images', image_path),  # app/reference_images
                                os.path.join('reference_images', image_path),  # reference_images in current dir
                            ]

                            # Try to use config.py for reference_images path
                            try:
                                # Add the current directory to the Python path to import config
                                import sys
                                # Use parent directory to import config from root
                                sys.path.insert(0, os.path.join(os.getcwd()))
                                import config
                                if hasattr(config, 'DIRECTORIES') and 'REFERENCE_IMAGES' in config.DIRECTORIES:
                                    config_ref_images = config.DIRECTORIES['REFERENCE_IMAGES']
                                    if isinstance(config_ref_images, str) or hasattr(config_ref_images, '__fspath__'):
                                        # Path object or string
                                        config_path = os.path.join(str(config_ref_images), image_path)
                                        self.logger.info(f"Checking config.py path: {config_path}")
                                        potential_paths.append(config_path)
                            except ImportError as e:
                                self.logger.warning(f"Could not import config.py from root: {e}")
                            except Exception as e:
                                self.logger.warning(f"Error getting reference_images from config.py: {e}")

                            # Log all paths being checked
                            self.logger.info(f"Checking these image paths:")
                            for idx, path in enumerate(potential_paths):
                                exists = os.path.exists(path)
                                status = "EXISTS" if exists else "NOT FOUND"
                                self.logger.info(f"  [{idx+1}] {path} - {status}")

                            # Try each path in sequence
                            found = False
                            for path in potential_paths:
                                if os.path.exists(path):
                                    image_path = path
                                    self.logger.info(f"Found image at: {image_path}")
                                    found = True
                                    break

                            if not found:
                                self.logger.error(f"Could not find image at any search path: {locator_value}")
                                return False

                    self.logger.info(f"Using image path: {image_path}")
                    template = Template(image_path)

                    # Use Airtest wait with the specified timeout
                    start_time = time.time()
                    found = False
                    position = None

                    # Try a direct wait if available
                    try:
                        position = airtest_api.wait(template, timeout=timeout, interval=interval)
                        found = True

                        # For iOS, apply scaling if needed
                        if hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS':
                            original_pos = position
                            position = self.scale_ios_coordinates(position)
                            self.logger.info(f"Applied iOS scaling: {original_pos} -> {position}")

                    except Exception as wait_err:
                        self.logger.warning(f"Airtest wait failed, falling back to periodic checks: {wait_err}")

                        # Fall back to periodic checks
                        while time.time() - start_time < timeout and not found:
                            try:
                                position = airtest_api.exists(template)
                                if position is not False and position is not None:
                                    found = True

                                    # For iOS, apply scaling if needed
                                    if hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS':
                                        original_pos = position
                                        position = self.scale_ios_coordinates(position)
                                        self.logger.info(f"Applied iOS scaling: {original_pos} -> {position}")

                                    break
                            except Exception as e:
                                self.logger.debug(f"Error in exists check: {e}")

                            time.sleep(interval)

                    if found:
                        self.logger.info(f"Image found at position: {position}")
                        return {
                            "status": "success",
                            "message": f"Image '{locator_value}' found at position {position}"
                        }
                    else:
                        self.logger.info(f"Image '{locator_value}' not found within timeout of {timeout} seconds")
                        return {
                            "status": "error",
                            "message": f"Image '{locator_value}' not found within timeout of {timeout} seconds"
                        }
                except ImportError:
                    self.logger.error("Airtest not imported, cannot use image locator")
                    return {
                        "status": "error",
                        "message": "Airtest not available for image recognition"
                    }
            elif locator_type == 'position':
                # Position is a special case - we'll use a custom handler
                # Expected format: "x,y,tolerance" or "x,y"
                try:
                    parts = locator_value.split(',')
                    x = int(parts[0].strip())
                    y = int(parts[1].strip())
                    tolerance = int(parts[2].strip()) if len(parts) > 2 else 10

                    self.logger.info(f"Waiting for element at position: ({x}, {y}) with tolerance {tolerance}px")

                    # We'll wait by checking if any element exists at the position periodically
                    start_time = time.time()
                    found = False
                    element = None

                    while time.time() - start_time < timeout:
                        try:
                            # Try to get element at position
                            element = self.device_controller.get_element_at_position(x, y, tolerance)
                            if element:
                                found = True
                                break
                        except Exception as e:
                            self.logger.debug(f"Error finding element at position: {str(e)}")

                        # Wait interval before retrying
                        time.sleep(interval)

                    if found and element:
                        # Get element details for better logging
                        element_info = "Unknown element"
                        try:
                            element_info = f"Element {element.tag_name}"
                            if element.text:
                                element_info += f" with text '{element.text}'"
                        except:
                            pass

                        return {
                            "status": "success",
                            "message": f"Element found at position ({x}, {y}): {element_info}"
                        }
                    else:
                        return {
                            "status": "error",
                            "message": f"No element found at position ({x}, {y}) within timeout of {timeout} seconds"
                        }

                except Exception as pos_error:
                    return {"status": "error", "message": f"Invalid position format. Expected 'x,y' or 'x,y,tolerance': {str(pos_error)}"}
            else:
                return {"status": "error", "message": f"Unsupported locator type: {locator_type}"}

            if locator:
                try:
                    # Use WebDriverWait to wait for the element with the given timeout
                    element = WebDriverWait(driver, timeout, interval).until(
                        EC.presence_of_element_located(locator)
                    )

                    # Get element details for better logging
                    element_info = "Unknown element"
                    try:
                        element_info = f"Element {element.tag_name}"
                        if element.text:
                            element_info += f" with text '{element.text}'"
                    except:
                        pass

                    self.logger.info(f"Element found: {element_info}")
                    return {
                        "status": "success",
                        "message": f"Element found with {locator_type} '{locator_value}': {element_info}"
                    }
                except TimeoutException:
                    self.logger.info(f"Element with {locator_type} '{locator_value}' not found within timeout of {timeout} seconds")
                    return {
                        "status": "error",
                        "message": f"Element with {locator_type} '{locator_value}' not found within timeout of {timeout} seconds"
                    }
                except NoSuchElementException:
                    self.logger.info(f"Element with {locator_type} '{locator_value}' not found")
                    return {
                        "status": "error",
                        "message": f"Element with {locator_type} '{locator_value}' not found"
                    }
        except Exception as e:
            self.logger.error(f"Failed to execute wait_till_element: {str(e)}")
            return {"status": "error", "message": f"Failed to execute wait_till_element: {str(e)}"}

    def _execute_text_clear(self, text, delay):
        """Execute a text clear action - clears the field first, then inputs text after a delay"""
        try:
            # Check if we're on iOS platform
            is_ios = hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name.lower() == 'ios'

            if is_ios and hasattr(self.device_controller, 'driver') and self.device_controller.driver:
                self.logger.info(f"Using iOS-specific clear and text input methods")

                # Method 1: Try using active element with clear() method
                try:
                    self.logger.info("Method 1: Using active element clear() method")
                    active_element = self.device_controller.driver.switch_to.active_element
                    active_element.clear()
                    self.logger.info("Cleared text field using active element clear()")
                except Exception as clear_error1:
                    self.logger.warning(f"Method 1 failed to clear text field: {clear_error1}")

                    # Method 2: Try using keyboard shortcuts (select all + delete)
                    try:
                        self.logger.info("Method 2: Using keyboard shortcuts to clear text")

                        # Try to select all text (cmd+a) and delete
                        active_element = self.device_controller.driver.switch_to.active_element
                        active_element.send_keys("\ue003\ue003\ue003\ue003\ue003\ue003\ue003\ue003\ue003\ue003")  # Send multiple backspaces
                        self.logger.info("Cleared text field using backspace keys")
                    except Exception as clear_error2:
                        self.logger.warning(f"Method 2 failed to clear text field: {clear_error2}")

                        # Method 3: Try using XCUITest clear method
                        try:
                            self.logger.info("Method 3: Using XCUITest clear method")
                            # Find the currently focused element
                            focused_element = self.device_controller.driver.find_element('xpath', '//*[@focused="true"]')
                            focused_element.clear()
                            self.logger.info("Cleared text field using XCUITest clear method")
                        except Exception as clear_error3:
                            self.logger.warning(f"Method 3 failed to clear text field: {clear_error3}")
                            self.logger.warning("All iOS clear methods failed, continuing with text input")
            else:
                # Standard method for non-iOS platforms
                try:
                    active_element = self.device_controller.driver.switch_to.active_element
                    active_element.clear()
                    self.logger.info("Cleared text field")
                except Exception as clear_error:
                    self.logger.error(f"Failed to clear text field: {clear_error}")
                    return {"status": "error", "message": f"Failed to clear text field: {str(clear_error)}"}

            # Wait for the specified delay
            delay_seconds = delay / 1000  # Convert ms to seconds
            time.sleep(delay_seconds)

            # Now input the new text
            input_result = self._execute_text(text)
            if input_result.get("status") == "error":
                return input_result

            return {"status": "success", "message": f"Cleared field and input text: {text} after {delay}ms"}
        except Exception as e:
            return {"status": "error", "message": f"Failed to clear and input text: {str(e)}"}

    def _execute_click_element(self, locator_type, locator_value, timeout):
        """Execute a click element action using various locator strategies"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException, NoSuchElementException

            # Make sure we have a driver in device controller
            if not hasattr(self.device_controller, 'driver') or not self.device_controller.driver:
                return {"status": "error", "message": "No Appium driver available for element clicking"}

            driver = self.device_controller.driver

            # Set up locator strategy
            locator = None
            if locator_type == 'id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ID, locator_value)
                self.logger.info(f"Clicking element with ID: {locator_value}")
            elif locator_type == 'xpath':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.XPATH, locator_value)
                self.logger.info(f"Clicking element with XPath: {locator_value}")
            elif locator_type == 'accessibility_id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ACCESSIBILITY_ID, locator_value)
                self.logger.info(f"Clicking element with Accessibility ID: {locator_value}")
            elif locator_type == 'text':
                from appium.webdriver.common.appiumby import AppiumBy
                # Create an XPath that searches for text
                xpath = f"//*[@text='{locator_value}' or contains(@content-desc, '{locator_value}')]"
                locator = (AppiumBy.XPATH, xpath)
                self.logger.info(f"Clicking element with text: {locator_value}")
            elif locator_type == 'position':
                # Position is a special case - we'll use a custom handler
                # Expected format: "x,y,tolerance" or "x,y"
                try:
                    parts = locator_value.split(',')
                    x = int(parts[0].strip())
                    y = int(parts[1].strip())
                    tolerance = int(parts[2].strip()) if len(parts) > 2 else 10

                    self.logger.info(f"Clicking element at position: ({x}, {y}) with tolerance {tolerance}px")

                    # We'll wait by checking if any element exists at the position periodically
                    start_time = time.time()
                    found = False
                    element = None

                    while time.time() - start_time < timeout:
                        try:
                            # Try to get element at position
                            element = self.device_controller.get_element_at_position(x, y, tolerance)
                            if element:
                                found = True
                                break
                        except Exception as e:
                            self.logger.debug(f"Error finding element at position: {str(e)}")

                        # Wait interval before retrying
                        time.sleep(0.5)

                    if found and element:
                        # Get element details for better logging
                        element_info = "Unknown element"
                        try:
                            element_info = f"Element {element.tag_name}"
                            if element.text:
                                element_info += f" with text '{element.text}'"
                        except:
                            pass

                        self.logger.info(f"Element found: {element_info}")
                        return {
                            "status": "success",
                            "message": f"Element found with {locator_type} '{locator_value}': {element_info}"
                        }
                    else:
                        return {
                            "status": "error",
                            "message": f"No element found at position ({x}, {y}) within timeout of {timeout} seconds"
                        }

                except Exception as pos_error:
                    return {"status": "error", "message": f"Invalid position format. Expected 'x,y' or 'x,y,tolerance': {str(pos_error)}"}
            else:
                return {"status": "error", "message": f"Unsupported locator type: {locator_type}"}

            if locator:
                try:
                    # Use WebDriverWait to wait for the element to be CLICKABLE with the given timeout
                    self.logger.info(f"Waiting for element to be clickable: {locator} (Timeout: {timeout}s)")
                    element = WebDriverWait(driver, timeout, 0.5).until(
                        EC.element_to_be_clickable(locator)
                    )

                    # Click the element
                    self.logger.info(f"Element is clickable, attempting click...")
                    element.click()

                    # Get element details for better logging
                    element_info = "Unknown element"
                    try:
                        element_info = f"Element {element.tag_name}"
                        if element.text:
                            element_info += f" with text '{element.text}'"
                    except:
                        pass

                    self.logger.info(f"Element clicked: {element_info}")
                    return {
                        "status": "success",
                        "message": f"Element clicked with {locator_type} '{locator_value}': {element_info}"
                    }
                except TimeoutException:
                    self.logger.info(f"Element with {locator_type} '{locator_value}' not clickable within timeout of {timeout} seconds")
                    return {
                        "status": "error",
                        "message": f"Element with {locator_type} '{locator_value}' not clickable within timeout of {timeout} seconds"
                    }
                except NoSuchElementException:
                    self.logger.info(f"Element with {locator_type} '{locator_value}' not found")
                    return {
                        "status": "error",
                        "message": f"Element with {locator_type} '{locator_value}' not found"
                    }
        except Exception as e:
            self.logger.error(f"Failed to execute click_element: {str(e)}")
            return {"status": "error", "message": f"Failed to execute click_element: {str(e)}"}

    def _execute_double_click_image(self, image_path, threshold, timeout):
        """Execute a double click on an image"""
        self.logger.info(f"Executing double click on image: {image_path} with threshold={threshold}, timeout={timeout}s")

        try:
            # Check if we have a device controller
            if not self.device_controller:
                return {"status": "error", "message": "No device controller available"}

            # Check if device is connected through Airtest
            if not hasattr(self.device_controller, 'airtest_device') or not self.device_controller.airtest_device:
                # Try to ensure Airtest connection is active
                if hasattr(self.device_controller, '_ensure_airtest_connected'):
                    airtest_connected = self.device_controller._ensure_airtest_connected()
                    if not airtest_connected:
                        # Check if this is an iOS simulator
                        if hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS':
                            # Additional help for iOS simulator issues
                            from airtest.core.api import device as current_device
                            if not current_device():
                                return {
                                    "status": "error",
                                    "message": "Could not connect to iOS simulator. Make sure WebDriverAgent (WDA) is running on port 8100."
                                }
                        return {"status": "error", "message": "No devices added. Please connect a device first."}
                else:
                    return {"status": "error", "message": "Device controller does not support Airtest"}

            # Try using the action factory first
            try:
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Ensure image path is valid
                if not os.path.exists(image_path):
                    # Try to resolve from reference_images directory
                    try:
                        from config import DIRECTORIES
                        reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                        if reference_dir:
                            full_path = os.path.join(reference_dir, os.path.basename(image_path))
                            if os.path.exists(full_path):
                                image_path = full_path
                                self.logger.info(f"Resolved image path to: {image_path}")
                    except (ImportError, Exception) as e:
                        self.logger.warning(f"Could not resolve reference image: {e}")

                # If still doesn't exist, return error
                if not os.path.exists(image_path):
                    return {"status": "error", "message": f"Image file not found: {image_path}"}

                params = {
                    'image_path': image_path,
                    'threshold': threshold,
                    'timeout': timeout
                }

                result = action_factory.execute_action('double_click_image', params)
                if result.get('status') == 'success':
                    return result

                # If that fails, try using Airtest directly
                if "No devices added" in result.get('message', '') or "Could not connect" in result.get('message', ''):
                    # Special case for "No devices added" error
                    if hasattr(self.device_controller, 'platform_name'):
                        if self.device_controller.platform_name == 'iOS':
                            return {
                                "status": "error",
                                "message": "Could not connect to iOS device. For simulators, make sure WebDriverAgent is running."
                            }
                        else:
                            return {
                                "status": "error",
                                "message": "No devices added. Please connect a device first."
                            }
                    return result  # Return original error if platform not determined

                self.logger.warning(f"First double_click_image attempt failed: {result.get('message')}")

                # Try using Airtest directly
                try:
                    from airtest.core.api import touch, Template, exists, device as current_device

                    # First check if any Airtest device is available
                    if not current_device():
                        # Special case for iOS simulators
                        if hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS':
                            return {
                                "status": "error",
                                "message": "Could not connect to iOS simulator. Make sure WebDriverAgent (WDA) is running on port 8100."
                            }
                        return {"status": "error", "message": "No devices added. Airtest device not available."}

                    # Verify image path exists one more time before creating Template
                    if not os.path.exists(image_path):
                        self.logger.error(f"Image file does not exist: {image_path}")
                        return {"status": "error", "message": f"Image file not found: {image_path}"}

                    # Log the absolute path to help debugging
                    abs_path = os.path.abspath(image_path)
                    self.logger.info(f"Using absolute image path: {abs_path}")

                    # Try to find and touch the template
                    template = Template(abs_path, threshold=threshold)
                    self.logger.info(f"Created Template object: {template}")

                    match_pos = exists(template)

                    if match_pos:
                        # Found the image, extract coordinates
                        center_x, center_y = match_pos
                        self.logger.info(f"Found image at: ({center_x}, {center_y})")

                        # Execute tap at the center position
                        touch(match_pos)
                        return {"status": "success", "message": f"Clicked image at position: ({center_x}, {center_y})"}
                    else:
                        self.logger.error(f"Image not found: {image_path}")
                        return {"status": "error", "message": f"Image not found: {image_path}"}

                except Exception as air_error:
                    error_msg = str(air_error)
                    self.logger.warning(f"Airtest touch failed: {error_msg}, falling back to OpenCV")

                    # Special case handling for common Airtest errors
                    if "No devices added" in error_msg:
                        if hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS':
                            return {
                                "status": "error",
                                "message": "Could not connect to iOS device with Airtest. For simulators, make sure WebDriverAgent is running."
                            }
                        return {"status": "error", "message": "No devices added. Please connect a device first."}

                    # OpenCV fallback implementation continues...
            except Exception as factory_error:
                self.logger.error(f"Error using action factory: {factory_error}")
                # If there's a problem with the action factory, continue to direct Airtest approach

            # The rest of the method remains unchanged

        except Exception as e:
            self.logger.error(f"Error in _execute_double_click_image: {str(e)}")
            error_msg = str(e)
            if "No devices added" in error_msg:
                # Special handling for iOS simulators
                if hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS':
                    return {
                        "status": "error",
                        "message": "Could not connect to iOS device with Airtest. For simulators, make sure WebDriverAgent is running."
                    }
                return {"status": "error", "message": "No devices added. Please connect a device first."}
            return {"status": "error", "message": f"Image click failed: {error_msg}"}

    def _execute_multiple_swipes(self, start_x, start_y, end_x, end_y, duration, count=1, interval=0.5):
        """Execute multiple swipes with intervals between them.

        Args:
            start_x (int): Starting X coordinate
            start_y (int): Starting Y coordinate
            end_x (int): Ending X coordinate
            end_y (int): Ending Y coordinate
            duration (int): Duration of swipe in milliseconds
            count (int): Number of swipes to perform
            interval (float): Time in seconds between swipes

        Returns:
            dict: Result with status and message
        """
        self.logger.info(f"Executing {count} swipe(s) from ({start_x},{start_y}) to ({end_x},{end_y})")

        try:
            success = True
            message = f"Performed {count} swipe(s) from ({start_x},{start_y}) to ({end_x},{end_y})"

            for i in range(count):
                self.logger.info(f"Executing swipe {i+1}/{count}")

                # Execute a single swipe
                result = self._execute_swipe(start_x, start_y, end_x, end_y, duration)

                # If any swipe fails, mark as failed but continue with the rest
                if not result.get('status') == 'success':
                    success = False
                    message = f"Swipe {i+1}/{count} failed: {result.get('message', 'Unknown error')}"
                    self.logger.warning(message)

                # Wait for the interval between swipes (except after the last one)
                if i < count - 1:
                    self.logger.info(f"Waiting {interval}s before next swipe")
                    time.sleep(interval)

            return {
                "status": "success" if success else "error",
                "message": message
            }
        except Exception as e:
            self.logger.error(f"Error executing multiple swipes: {e}")
            return {
                "status": "error",
                "message": f"Multiple swipes failed: {str(e)}"
            }

    def _execute_click_image(self, image_path, threshold=0.7, timeout=20):
        """Execute click on an image using Airtest or a compatible method"""
        self.logger.info(f"Executing click on image: {image_path} with threshold={threshold}, timeout={timeout}s")

        try:
            # Check if we have a device controller
            if not self.device_controller:
                return {"status": "error", "message": "No device controller available"}

            # Check if device is connected through Airtest
            if not hasattr(self.device_controller, 'airtest_device') or not self.device_controller.airtest_device:
                # Try to ensure Airtest connection is active
                if hasattr(self.device_controller, '_ensure_airtest_connected'):
                    airtest_connected = self.device_controller._ensure_airtest_connected()
                    if not airtest_connected:
                        # Check if this is an iOS simulator
                        if hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS':
                            # Additional help for iOS simulator issues
                            from airtest.core.api import device as current_device
                            if not current_device():
                                return {
                                    "status": "error",
                                    "message": "Could not connect to iOS simulator. Make sure WebDriverAgent (WDA) is running on port 8100."
                                }
                        return {"status": "error", "message": "No devices added. Please connect a device first."}
                else:
                    return {"status": "error", "message": "Device controller does not support Airtest"}

            # Try using the action factory first
            try:
                from actions.action_factory import ActionFactory
                action_factory = ActionFactory(self.device_controller)

                # Ensure image path is valid
                if not os.path.exists(image_path):
                    # Try to resolve from reference_images directory
                    try:
                        from config import DIRECTORIES
                        reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                        if reference_dir:
                            full_path = os.path.join(reference_dir, os.path.basename(image_path))
                            if os.path.exists(full_path):
                                image_path = full_path
                                self.logger.info(f"Resolved image path to: {image_path}")
                    except (ImportError, Exception) as e:
                        self.logger.warning(f"Could not resolve reference image: {e}")

                # If still doesn't exist, return error
                if not os.path.exists(image_path):
                    return {"status": "error", "message": f"Image file not found: {image_path}"}

                params = {
                    'image_path': image_path,
                    'threshold': threshold,
                    'timeout': timeout
                }

                result = action_factory.execute_action('click_image', params)
                if result.get('status') == 'success':
                    return result

                # If that fails, try using Airtest directly
                if "No devices added" in result.get('message', '') or "Could not connect" in result.get('message', ''):
                    # Special case for "No devices added" error
                    if hasattr(self.device_controller, 'platform_name'):
                        if self.device_controller.platform_name == 'iOS':
                            return {
                                "status": "error",
                                "message": "Could not connect to iOS device. For simulators, make sure WebDriverAgent is running."
                            }
                        else:
                            return {
                                "status": "error",
                                "message": "No devices added. Please connect a device first."
                            }
                    return result  # Return original error if platform not determined

                self.logger.warning(f"First click_image attempt failed: {result.get('message')}")

                # Try using Airtest directly
                try:
                    from airtest.core.api import touch, Template, exists, device as current_device

                    # First check if any Airtest device is available
                    if not current_device():
                        # Special case for iOS simulators
                        if hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS':
                            return {
                                "status": "error",
                                "message": "Could not connect to iOS simulator. Make sure WebDriverAgent (WDA) is running on port 8100."
                            }
                        return {"status": "error", "message": "No devices added. Airtest device not available."}

                    # Verify image path exists one more time before creating Template
                    if not os.path.exists(image_path):
                        self.logger.error(f"Image file does not exist: {image_path}")
                        return {"status": "error", "message": f"Image file not found: {image_path}"}

                    # Log the absolute path to help debugging
                    abs_path = os.path.abspath(image_path)
                    self.logger.info(f"Using absolute image path: {abs_path}")

                    # Try to find and touch the template
                    template = Template(abs_path, threshold=threshold)
                    self.logger.info(f"Created Template object: {template}")

                    match_pos = exists(template)

                    if match_pos:
                        # Found the image, extract coordinates
                        center_x, center_y = match_pos
                        self.logger.info(f"Found image at: ({center_x}, {center_y})")

                        # Execute tap at the center position
                        touch(match_pos)
                        return {"status": "success", "message": f"Clicked image at position: ({center_x}, {center_y})"}
                    else:
                        self.logger.error(f"Image not found: {image_path}")
                        return {"status": "error", "message": f"Image not found: {image_path}"}

                except Exception as air_error:
                    error_msg = str(air_error)
                    self.logger.warning(f"Airtest touch failed: {error_msg}, falling back to OpenCV")

                    # Special case handling for common Airtest errors
                    if "No devices added" in error_msg:
                        if hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS':
                            return {
                                "status": "error",
                                "message": "Could not connect to iOS device with Airtest. For simulators, make sure WebDriverAgent is running."
                            }
                        return {"status": "error", "message": "No devices added. Please connect a device first."}

                    # OpenCV fallback implementation continues...
            except Exception as factory_error:
                self.logger.error(f"Error using action factory: {factory_error}")
                # If there's a problem with the action factory, continue to direct Airtest approach

            # The rest of the method remains unchanged

        except Exception as e:
            self.logger.error(f"Error in _execute_click_image: {str(e)}")
            error_msg = str(e)
            if "No devices added" in error_msg:
                # Special handling for iOS simulators
                if hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS':
                    return {
                        "status": "error",
                        "message": "Could not connect to iOS device with Airtest. For simulators, make sure WebDriverAgent is running."
                    }
                return {"status": "error", "message": "No devices added. Please connect a device first."}
            return {"status": "error", "message": f"Image click failed: {error_msg}"}

    def _execute_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators=None, fallback_type=None, fallback_params=None):
        """Execute a tap action using a locator with fallback support

        Args:
            locator_type (str): Primary locator type
            locator_value (str): Primary locator value
            timeout (int): Timeout in seconds
            interval (float): Polling interval in seconds
            fallback_locators (list): List of fallback locators to try if primary fails
            fallback_type (str): Type of fallback to use (coordinates, image, text, locator)
            fallback_params (dict): Parameters for the fallback action

        Returns:
            dict: Result with status and message
        """
        # Use the action factory to handle fallback locators properly
        from actions.action_factory import ActionFactory
        action_factory = ActionFactory(self.device_controller)

        # Prepare parameters for the tap action
        params = {
            'method': 'locator',
            'locator_type': locator_type,
            'locator_value': locator_value,
            'timeout': timeout,
            'interval': interval
        }

        # Add fallback locators if available
        if fallback_locators:
            self.logger.info(f"Using TapAction with fallback locators support")
            params['fallback_locators'] = fallback_locators

        # Add new fallback parameters if available
        if fallback_type:
            self.logger.info(f"Using TapAction with new fallback type: {fallback_type}")
            params['fallback_type'] = fallback_type

            # Add fallback parameters based on type
            if fallback_type == 'coordinates':
                if fallback_params:
                    params['fallback_x'] = fallback_params.get('fallback_x')
                    params['fallback_y'] = fallback_params.get('fallback_y')
            elif fallback_type == 'image':
                if fallback_params:
                    params['fallback_image_filename'] = fallback_params.get('fallback_image_filename')
                    params['fallback_threshold'] = fallback_params.get('fallback_threshold', 0.7)
            elif fallback_type == 'text':
                if fallback_params:
                    params['fallback_text'] = fallback_params.get('fallback_text')
            elif fallback_type == 'locator':
                if fallback_params:
                    params['fallback_locator_type'] = fallback_params.get('fallback_locator_type')
                    params['fallback_locator_value'] = fallback_params.get('fallback_locator_value')

        # Execute the tap action with fallback support
        return action_factory.execute_action('tap', params)

    def _execute_double_tap_with_locator(self, locator_type, locator_value, timeout, interval, fallback_locators=None):
        """Execute a double tap action using a locator with fallback support

        Args:
            locator_type (str): Primary locator type
            locator_value (str): Primary locator value
            timeout (int): Timeout in seconds
            interval (float): Polling interval in seconds
            fallback_locators (list): List of fallback locators to try if primary fails

        Returns:
            dict: Result with status and message
        """
        # Use the action factory to handle fallback locators properly
        from actions.action_factory import ActionFactory

        # If we have fallback locators, use the ActionFactory which has proper fallback support
        if fallback_locators:
            self.logger.info(f"Using ActionFactory with fallback locators support for double tap")
            action_factory = ActionFactory(self.device_controller)

            # Prepare parameters for the double tap action
            params = {
                'method': 'locator',
                'locator_type': locator_type,
                'locator_value': locator_value,
                'timeout': timeout,
                'interval': interval,
                'fallback_locators': fallback_locators
            }

            # Log available action types for debugging
            self.logger.info(f"Available action types before doubleTap in _execute_double_tap_with_locator: {sorted(list(action_factory.action_handlers.keys()))}")

            # Execute the double tap action with fallback support
            # Note: We use 'doubleTap' action type here to match the action type in the action factory
            self.logger.info(f"Executing doubleTap action with params: {params}")
            result = action_factory.execute_action('doubleTap', params)
            self.logger.info(f"doubleTap action result: {result}")
            return result

        # If no fallback locators, use the original implementation
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException, NoSuchElementException

            # Make sure we have a driver in device controller
            if not hasattr(self.device_controller, 'driver') or not self.device_controller.driver:
                return {"status": "error", "message": "No Appium driver available for element clicking"}

            driver = self.device_controller.driver

            # Set up locator strategy
            locator = None
            if locator_type == 'id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ID, locator_value)
                self.logger.info(f"Double-tapping element with ID: {locator_value}")
            elif locator_type == 'xpath':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.XPATH, locator_value)
                self.logger.info(f"Double-tapping element with XPath: {locator_value}")
            elif locator_type == 'accessibility_id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ACCESSIBILITY_ID, locator_value)
                self.logger.info(f"Double-tapping element with Accessibility ID: {locator_value}")
            elif locator_type == 'text':
                from appium.webdriver.common.appiumby import AppiumBy
                # Create an XPath that searches for text
                xpath = f"//*[@text='{locator_value}' or contains(@content-desc, '{locator_value}')]"
                locator = (AppiumBy.XPATH, xpath)
                self.logger.info(f"Double-tapping element with text: {locator_value}")
            elif locator_type == 'position':
                # Position is a special case - we'll use a custom handler
                # Expected format: "x,y,tolerance" or "x,y"
                try:
                    parts = locator_value.split(',')
                    x = int(parts[0].strip())
                    y = int(parts[1].strip())

                    # Just perform a double-tap at these coordinates
                    return self._execute_double_click(x, y)

                except Exception as pos_error:
                    return {"status": "error", "message": f"Invalid position format. Expected 'x,y' or 'x,y,tolerance': {str(pos_error)}"}
            else:
                return {"status": "error", "message": f"Unsupported locator type: {locator_type}"}

            if locator:
                try:
                    # Use WebDriverWait to wait for the element to be CLICKABLE with the given timeout
                    self.logger.info(f"Waiting for element to be clickable: {locator} (Timeout: {timeout}s)")
                    element = WebDriverWait(driver, timeout, 0.5).until(
                        EC.element_to_be_clickable(locator)
                    )

                    # Get element details for better logging
                    element_info = "Unknown element"
                    try:
                        element_info = f"Element {element.tag_name}"
                        if element.text:
                            element_info += f" with text '{element.text}'"
                    except:
                        pass

                    # Try to perform double tap using TouchActions if available
                    try:
                        from appium.webdriver.common.touch_action import TouchAction
                        self.logger.info(f"Attempting double tap using TouchAction...")

                        action = TouchAction(driver)
                        action.tap(element).wait(100).tap(element).perform()

                        self.logger.info(f"Element double-tapped: {element_info}")
                        return {
                            "status": "success",
                            "message": f"Element double-tapped with {locator_type} '{locator_value}': {element_info}"
                        }
                    except ImportError:
                        self.logger.info("TouchAction not available, trying alternative method...")
                    except Exception as touch_error:
                        self.logger.warning(f"TouchAction failed: {str(touch_error)}, trying alternative method...")

                    # Try W3C actions if TouchAction fails
                    try:
                        from selenium.webdriver.common.action_chains import ActionChains
                        self.logger.info(f"Attempting double tap using ActionChains...")

                        actions = ActionChains(driver)
                        actions.double_click(element).perform()

                        self.logger.info(f"Element double-clicked: {element_info}")
                        return {
                            "status": "success",
                            "message": f"Element double-clicked with {locator_type} '{locator_value}': {element_info}"
                        }
                    except Exception as action_error:
                        self.logger.warning(f"ActionChains failed: {str(action_error)}, trying fallback method...")

                    # Fallback to two clicks with a small delay
                    self.logger.info(f"Using fallback method: two clicks with delay...")
                    element.click()
                    time.sleep(0.1)  # Small delay between clicks
                    element.click()

                    self.logger.info(f"Element double-clicked (fallback method): {element_info}")
                    return {
                        "status": "success",
                        "message": f"Element double-clicked with {locator_type} '{locator_value}': {element_info}"
                    }
                except TimeoutException:
                    self.logger.info(f"Element with {locator_type} '{locator_value}' not clickable within timeout of {timeout} seconds")
                    return {
                        "status": "error",
                        "message": f"Element with {locator_type} '{locator_value}' not clickable within timeout of {timeout} seconds"
                    }
                except NoSuchElementException:
                    self.logger.info(f"Element with {locator_type} '{locator_value}' not found")
                    return {
                        "status": "error",
                        "message": f"Element with {locator_type} '{locator_value}' not found"
                    }
        except Exception as e:
            self.logger.error(f"Failed to execute double_tap_with_locator: {str(e)}")
            return {"status": "error", "message": f"Failed to execute double_tap_with_locator: {str(e)}"}

    def _execute_swipe_till_visible(self, action):
        try:
            screen_size = self.device_controller.get_device_dimensions() or (1080, 1920)
            vector_start = action.get('vector_start', [0.5, 0.5])
            vector_end = action.get('vector_end', [0.5, 0.7])
            duration = action.get('duration', 300)
            count = int(action.get('count', 1))
            interval = float(action.get('interval', 0.5))
            locator_type = action.get('locator_type')
            locator_value = action.get('locator_value')
            image_filename = action.get('image_filename')

            # Log the parameters for debugging
            self.logger.info(f"SwipeTillVisible params - locator_type: '{locator_type}', locator_value: '{locator_value}', image_filename: '{image_filename}'")

            # If image_filename is provided but locator_value is empty, or if locator type is image,
            # use image_filename as locator value
            if locator_type == 'image' and image_filename:
                locator_value = image_filename
                self.logger.info(f"Using image_filename as locator_value: {image_filename}")
            elif image_filename and (not locator_value or locator_value.strip() == ""):
                locator_type = 'image'
                locator_value = image_filename
                self.logger.info(f"Using image_filename as locator: {image_filename}")

            if not locator_type or not locator_value:
                return {"status": "error", "message": "Missing locator type or value"}

            if vector_start[0] > 1 or vector_start[1] > 1:
                vector_start = [val/100 for val in vector_start]
            if vector_end[0] > 1 or vector_end[1] > 1:
                vector_end = [val/100 for val in vector_end]

            start_x = int(vector_start[0] * screen_size[0])
            start_y = int(vector_start[1] * screen_size[1])
            end_x = int(vector_end[0] * screen_size[0])
            end_y = int(vector_end[1] * screen_size[1])

            self.logger.info(f"Executing swipeTillVisible: swiping from ({start_x}, {start_y}) to ({end_x}, {end_y})")

            for i in range(count):
                self.device_controller.swipe(start_x, start_y, end_x, end_y, duration)
                import time
                time.sleep(interval)
                if self._is_element_visible(locator_type, locator_value):
                    return {"status": "success", "message": f"Element visible after {i+1} swipe(s)"}
            return {"status": "error", "message": f"Element not visible after {count} swipe(s)"}
        except Exception as e:
            self.logger.error(f"Error in swipeTillVisible execution: {e}")
            return {"status": "error", "message": str(e)}

    def _is_element_visible(self, locator_type, locator_value):
        """Check if element is visible using the appropriate method based on locator type."""
        try:
            # Handle image locator type
            if locator_type.lower() == 'image':
                try:
                    # Use our new airtest_utils module for image finding and matching
                    from app.utils.airtest_utils import find_image_on_screen

                    self.logger.info(f"Checking if image is visible: {locator_value}")

                    # Get iOS platform info
                    is_ios = hasattr(self.device_controller, 'platform_name') and self.device_controller.platform_name == 'iOS'
                    platform = "ios" if is_ios else "android"

                    # Set threshold based on platform
                    threshold = 0.6 if is_ios else 0.7

                    # Use our new utility function to find the image
                    success, position = find_image_on_screen(
                        image_filename=locator_value,
                        platform=platform,
                        threshold=threshold,
                        scale_factor=None,  # Use default or environment variable
                        save_debug=False  # Don't save debug images by default
                    )

                    if success:
                        self.logger.info(f"✅ Image found at position: {position}")
                        return True
                    else:
                        self.logger.info(f"❌ Image not found: {locator_value}")
                        return False

                except Exception as image_err:
                    self.logger.error(f"Error finding image: {image_err}")
                    return False
            driver = self.device_controller.driver
            if not driver:
                return False

            # Import AppiumBy for modern locator strategies
            from appium.webdriver.common.appiumby import AppiumBy

            locator_type = locator_type.lower()

            # Map locator type to AppiumBy constant
            locator_map = {
                'id': AppiumBy.ID,
                'xpath': AppiumBy.XPATH,
                'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
                'accessibilityid': AppiumBy.ACCESSIBILITY_ID,
                'text': AppiumBy.XPATH,  # Special handling below
                'name': AppiumBy.NAME,
                'class name': AppiumBy.CLASS_NAME
            }

            # Get the locator strategy
            locator_strategy = locator_map.get(locator_type)
            if not locator_strategy:
                self.logger.warning(f"Unsupported locator type: {locator_type}, trying as xpath")
                locator_strategy = AppiumBy.XPATH

            # Special handling for text locator type
            if locator_type == 'text':
                locator_value = f"//*[contains(@text, '{locator_value}')]"

            # Use modern find_elements method
            elements = driver.find_elements(locator_strategy, locator_value)
            if len(elements) > 0:
                self.logger.info(f"Element found using {locator_type}='{locator_value}'")
                # Check if the element is displayed
                return elements[0].is_displayed()
            else:
                return False
        except Exception as e:
            self.logger.info(f"Element not visible using {locator_type}='{locator_value}': {e}")
            return False

    def scale_ios_coordinates(self, coordinates):
        """Scale coordinates for iOS devices to account for the difference
        between logical and physical coordinates."""
        # Use the scale_ios_coordinates function from our airtest_utils module
        from app.utils.airtest_utils import scale_ios_coordinates as scale_coords
        return scale_coords(coordinates)