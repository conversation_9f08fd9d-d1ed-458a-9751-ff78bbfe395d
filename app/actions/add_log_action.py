from app.actions.base_action import BaseAction
from airtest.core.helper import log
import logging
import os
from app.utils.screenshot_manager import screenshot_manager

class AddLogAction(BaseAction):
    """Handler for adding log entries with optional screenshots"""

    def execute(self, params):
        """
        Execute add log action

        Args:
            params: Dictionary containing:
                - message: The log message to add
                - take_screenshot: (Optional) Whether to take a screenshot (default: True)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        message = params.get('message', '')
        take_screenshot = params.get('take_screenshot', True)
        action_id = params.get('action_id')

        # If no action_id is provided, generate one
        if not action_id:
            import random
            import string
            chars = string.ascii_letters + string.digits
            action_id = ''.join(random.choice(chars) for _ in range(10))
            self.logger.info(f"Generated action_id for addLog action: {action_id}")
        else:
            # If action_id starts with 'al_', remove the prefix for consistent naming
            if action_id.startswith('al_'):
                self.logger.info(f"Removing 'al_' prefix from action_id: {action_id}")
                action_id = action_id[3:]
                self.logger.info(f"New action_id without prefix: {action_id}")
            # Log the final action_id that will be used
            self.logger.info(f"Using action_id for screenshot: {action_id}")

        # Store the action_id in the result to ensure it's returned to the client
        result_action_id = action_id

        if not message:
            return {"status": "error", "message": "Missing message parameter"}

        try:
            # Use Airtest's log function to add a log entry with optional screenshot
            self.logger.info(f"Adding log entry: {message}")

            # Check if Airtest is available
            try:
                # Take screenshot if requested
                screenshot_path = None
                if take_screenshot:
                    # Get the current test case and step indices from app.py
                    from app.app import current_suite_id
                    try:
    from ..utils.database import save_screenshot_info

                    if screenshot_path and current_suite_id
except ImportError:
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from utils.database import save_screenshot_info

                    if screenshot_path and current_suite_id:
                        save_screenshot_info(
                            suite_id=current_suite_id,
                            test_idx=test_idx,
                            step_idx=step_idx,
                            filename=os.path.basename(screenshot_path),
                            path=screenshot_path,
                            action_id=action_id
                        )
                        self.logger.info(f"Saved screenshot info to database with action_id: {action_id}")
                except Exception as db_error:
                    self.logger.error(f"Error saving screenshot info to database: {str(db_error)}")

                return {
                    "status": "success",
                    "message": message,  # Use just the message without prefix
                    "screenshot": screenshot_path,
                    "screenshot_filename": os.path.basename(screenshot_path) if screenshot_path else None,
                    "action_id": result_action_id
                }
            except Exception as airtest_error:
                self.logger.warning(f"Could not use Airtest log function: {airtest_error}")

                # Fallback to regular logging
                self.logger.info(f"Log entry (fallback): {message}")

                # Use the screenshot that was already taken in the main try block
                # Don't take another screenshot in the fallback path
                screenshot_path = None
                if take_screenshot:
                    # Get the current test case and step indices from app.py
                    from app.app import current_test_idx, current_step_idx
                    test_idx # Import os here to ensure it's available in this scope
                    if current_screenshots_dir and os.path.exists(current_screenshots_dir):
                        # Use the existing screenshot if available
                        # Look for existing addlog_image files
                        import glob
                        existing_screenshots = glob.glob(os.path.join(current_screenshots_dir, "addlog_image_*.png"))
                        if existing_screenshots:
                            # Sort by modification time, newest first
                            existing_screenshots.sort(key=os.path.getmtime, reverse=True)
                            screenshot_path_full = existing_screenshots[0]
                            self.logger.info(f"AddLog (fallback): Using existing screenshot: {screenshot_path_full}")
                            screenshot_result = {"status": "success", "path": screenshot_path_full}
                        else:
                            # If no existing screenshot, create a new one
                            # Get the current image counter from app.py or initialize it
                            from app.app import current_image_counter
                            image_number 
                        else:
                            screenshot_path = screenshot_result

                # Ensure screenshot_path is a string or None, not a dict
                if isinstance(screenshot_path, dict):
                    self.logger.warning(f"Final screenshot_path is still a dict, extracting 'path' value: {screenshot_path}")
                    screenshot_path = screenshot_path.get('path')

                # Additional safety check to ensure screenshot_path is a string or None
                if screenshot_path is not None and not isinstance(screenshot_path, (str, bytes, os.PathLike)):
                    self.logger.warning(f"Screenshot path is not a string, bytes, or PathLike object: {type(screenshot_path)}. Converting to string.")
                    try:
                        screenshot_path = str(screenshot_path)
                    except:
                        self.logger.error(f"Failed to convert screenshot_path to string: {screenshot_path}")
                        screenshot_path = None

                # Save screenshot info to database with action_id
                try:
                    from app.app import current_suite_id
                    try:
    from ..utils.database import save_screenshot_info

                    if screenshot_path and current_suite_id
except ImportError:
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from utils.database import save_screenshot_info

                    if screenshot_path and current_suite_id:
                        save_screenshot_info(
                            suite_id=current_suite_id,
                            test_idx=test_idx,
                            step_idx=step_idx,
                            filename=os.path.basename(screenshot_path),
                            path=screenshot_path,
                            action_id=action_id
                        )
                        self.logger.info(f"Saved screenshot info to database with action_id: {action_id} (fallback)")
                except Exception as db_error:
                    self.logger.error(f"Error saving screenshot info to database (fallback): {str(db_error)}")

                return {
                    "status": "success",
                    "message": message,  # Use just the message without prefix
                    "screenshot": screenshot_path,
                    "screenshot_filename": os.path.basename(screenshot_path) if screenshot_path else None,
                    "action_id": result_action_id
                }

        except Exception as e:
            self.logger.error(f"Error executing add log action: {e}")
            return {"status": "error", "message": f"Add log action failed: {str(e)}"}
