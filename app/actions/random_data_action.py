"""
Random Data Action

This action generates random data using the Mimesis library during test execution.
"""

from base_action import BaseAction
import logging

class RandomDataAction(BaseAction):
    """Handler for generating random data during test execution"""

    def execute(self, params):
        """
        Execute random data generation action

        Args:
            params: Dictionary containing:
                - generator_id: ID of the generator to use
                - param_name: (Optional) Name of the parameter to store the generated data

        Returns:
            dict: Result with status, message, and generated data
        """
        if not params:
            return {"status": "error", "message": "No parameters provided"}

        generator_id = params.get('generator_id')
        param_name = params.get('param_name')

        if not generator_id:
            return {"status": "error", "message": "Missing required parameter: generator_id"}

        try:
            # Import the random data generator
            from app.utils.random_data_generator import generate_data

            # Generate the random data
            generated_data = generate_data(generator_id)

            # If a parameter name is provided, store the generated data
            if param_name:
                try:
                    # Import the global values database
                    import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from global_values_db import GlobalValuesDB
                    
                    # Store the generated data in the database
                    global_values_db = GlobalValuesDB()
                    global_values_db.set_value(param_name, generated_data)
                    
                    self.logger.info(f"Stored generated data '{generated_data}' in parameter '{param_name}'")
                except Exception as param_error:
                    self.logger.error(f"Error storing generated data in parameter: {param_error}")
                    return {
                        "status": "error", 
                        "message": f"Generated data but failed to store in parameter: {str(param_error)}",
                        "data": generated_data
                    }

            return {
                "status": "success",
                "message": f"Generated random data: {generated_data}" + (f" (stored in parameter: {param_name})" if param_name else ""),
                "data": generated_data
            }

        except Exception as e:
            self.logger.error(f"Error executing random data generation action: {e}")
            return {"status": "error", "message": f"Random data generation failed: {str(e)}"}
