from base_action import BaseAction
import logging
import traceback
import time

class RepeatStepsAction(BaseAction):
    """Handler for executing a test case steps repeatedly"""

    def execute(self, params):
        """
        Execute a test case steps repeatedly for a specified number of times

        Args:
            params: Dictionary containing:
                - test_case_id: ID of the test case to execute
                - test_case_steps: Steps of the test case to execute
                - repeat_count: Number of times to repeat the steps

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get the test case ID from params
        test_case_id = params.get('test_case_id')
        test_case_steps = params.get('test_case_steps', [])
        repeat_count = params.get('repeat_count', 1)

        # Convert repeat_count to integer with fallback to 1
        try:
            repeat_count = int(repeat_count)
            if repeat_count < 1:
                repeat_count = 1
        except (ValueError, TypeError):
            repeat_count = 1
            
        self.logger.info(f"Repeat count set to: {repeat_count}")

        if not test_case_id:
            return {"status": "error", "message": "No test case ID provided"}

        # If no steps are provided directly, try to load them from the test case file
        if not test_case_steps:
            self.logger.info(f"No test case steps provided directly, attempting to load from file: {test_case_id}")
            try:
                # Import the test case manager
                from app.utils.test_case_manager import TestCaseManager

                # Create a test case manager instance
                test_case_manager = TestCaseManager()

                # Load the test case
                test_case = test_case_manager.load_test_case(test_case_id)

                if test_case and 'actions' in test_case:
                    test_case_steps = test_case.get('actions', [])
                    self.logger.info(f"Loaded {len(test_case_steps)} steps from test case file: {test_case_id}")
                else:
                    self.logger.error(f"Failed to load test case or no actions found: {test_case_id}")
                    return {"status": "error", "message": f"Failed to load test case or no actions found: {test_case_id}"}
            except Exception as e:
                self.logger.error(f"Error loading test case: {e}")
                return {"status": "error", "message": f"Error loading test case: {str(e)}"}

        if not test_case_steps:
            return {"status": "error", "message": "No test case steps provided or loaded"}

        self.logger.info(f"Executing repeat steps action for test case: {test_case_id} ({repeat_count} times)")

        # Import the action factory
        from action_factory import ActionFactory

        # Create a new action factory with the same controller
        action_factory = ActionFactory(self.controller)

        # Execute each step in the test case for the specified number of times
        results = []
        success_count = 0
        total_steps = len(test_case_steps) * repeat_count

        # Find all hook actions in the test case steps
        # Look for both 'action_type' and 'type' being 'hookAction'
        hook_actions = [step for step in test_case_steps if step.get('action_type') == 'hookAction' or step.get('type') == 'hookAction']
        if hook_actions:
            self.logger.info(f"Found {len(hook_actions)} hook actions within repeat steps")
            # Log the hook actions for debugging
            for i, hook in enumerate(hook_actions):
                self.logger.info(f"Hook action {i+1}: {hook}")

        # Execute the steps repeat_count times
        for repeat_idx in range(repeat_count):
            self.logger.info(f"Starting repetition {repeat_idx + 1} of {repeat_count}")
            
            step_index = 0
            while step_index < len(test_case_steps):
                step = test_case_steps[step_index]
                try:
                    # Calculate overall step number for logging
                    overall_step = (repeat_idx * len(test_case_steps)) + step_index + 1
                    
                    # Log the step being executed
                    self.logger.info(f"Executing step {overall_step}/{total_steps} (repetition {repeat_idx + 1}, step {step_index + 1}): {step.get('action_type', 'unknown')}")

                    # Execute the step using the action factory
                    action_type = step.get('action_type')
                    if not action_type:
                        self.logger.warning(f"Step {step_index + 1} has no action_type, skipping")
                        results.append({
                            "status": "warning",
                            "message": f"Step {step_index + 1} has no action_type, skipping",
                            "step_index": step_index,
                            "repeat_index": repeat_idx
                        })
                        step_index += 1
                        continue

                    # Skip hook actions during normal execution
                    if action_type == 'hookAction' or step.get('type') == 'hookAction':
                        # Skip hook actions during normal execution
                        self.logger.info(f"Skipping hook action during normal execution (repetition {repeat_idx + 1}, step {step_index + 1})")
                        results.append({
                            "status": "success",
                            "message": "Hook Action skipped (will only be executed when a step fails)",
                            "step_index": step_index,
                            "repeat_index": repeat_idx
                        })
                        success_count += 1
                        step_index += 1
                        continue

                    # Execute the action
                    result = action_factory.execute_action(action_type, step)

                    # Add the step index and repeat index to the result
                    if isinstance(result, dict):
                        result['step_index'] = step_index
                        result['repeat_index'] = repeat_idx
                        results.append(result)

                        # Check if the step failed
                        if result.get('status') != 'success':
                            self.logger.error(f"Step {overall_step} failed (repetition {repeat_idx + 1}, step {step_index + 1}): {result.get('message')}")

                            # If we have hook actions in the repeat steps, execute them
                            if hook_actions:
                                self.logger.info(f"Executing {len(hook_actions)} hook actions within repeat steps due to step failure")

                                # Execute each hook action
                                hook_success = False
                                for hook_index, hook_action in enumerate(hook_actions):
                                    self.logger.info(f"Executing hook action {hook_index + 1}/{len(hook_actions)}")

                                    # Get the hook type and data
                                    hook_type = hook_action.get('hook_type')
                                    hook_data = hook_action.get('hook_data', {})

                                    if not hook_type:
                                        self.logger.warning(f"Hook action {hook_index + 1} has no hook_type, skipping")
                                        continue

                                    # Create a new action with the hook type and data
                                    recovery_action = {'type': hook_type}

                                    # Copy all hook_data to the recovery action
                                    for key, value in hook_data.items():
                                        recovery_action[key] = value

                                    self.logger.info(f"Executing hook action: {hook_type}")

                                    # Execute the hook action
                                    hook_result = action_factory.execute_action(hook_type, recovery_action)

                                    # Check if the hook action succeeded
                                    if isinstance(hook_result, dict) and hook_result.get('status') == 'success':
                                        self.logger.info(f"Hook action {hook_index + 1} succeeded")
                                        hook_success = True
                                    else:
                                        self.logger.error(f"Hook action {hook_index + 1} failed: {hook_result}")

                                # If any hook action succeeded, retry the failed step
                                if hook_success:
                                    self.logger.info(f"Retrying failed step {overall_step} after hook actions")
                                    continue  # Retry the same step

                            # If no hook actions or all hook actions failed, move to the next step
                            step_index += 1
                        else:
                            # Step succeeded, count it and move to the next step
                            success_count += 1
                            step_index += 1
                    else:
                        self.logger.warning(f"Step {overall_step} returned non-dict result: {result}")
                        results.append({
                            "status": "warning",
                            "message": f"Step {overall_step} returned non-dict result: {result}",
                            "step_index": step_index,
                            "repeat_index": repeat_idx
                        })
                        step_index += 1

                except Exception as e:
                    self.logger.error(f"Error executing step {step_index + 1} in repetition {repeat_idx + 1}: {e}")
                    self.logger.error(traceback.format_exc())
                    results.append({
                        "status": "error",
                        "message": f"Error executing step {step_index + 1} in repetition {repeat_idx + 1}: {str(e)}",
                        "step_index": step_index,
                        "repeat_index": repeat_idx
                    })
                    step_index += 1

            # Log completion of this repetition
            self.logger.info(f"Completed repetition {repeat_idx + 1} of {repeat_count}")

        # Determine overall status
        if success_count == total_steps:
            overall_status = "success"
            message = f"All {success_count} steps across {repeat_count} repetitions executed successfully"
        elif success_count > 0:
            overall_status = "partial"
            message = f"{success_count}/{total_steps} steps across {repeat_count} repetitions executed successfully"
        else:
            overall_status = "error"
            message = "All steps failed to execute"

        return {
            "status": overall_status,
            "message": message,
            "result_details": {
                "total_steps": total_steps,
                "success_count": success_count,
                "repeat_count": repeat_count,
                "test_case_id": test_case_id,
                "steps_per_repetition": len(test_case_steps)
            },
            "results": results
        } 