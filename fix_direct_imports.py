#!/usr/bin/env python3
"""
<PERSON><PERSON>t to fix import statements in action files to use direct imports
"""

import os
import re
import glob

def fix_action_file_imports(file_path):
    """Fix imports in a single action file to use direct imports"""
    print(f"Fixing imports in {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Fix app.actions imports to direct imports
    content = re.sub(
        r'from app\.actions\.base_action import BaseAction',
        'from base_action import BaseAction',
        content
    )
    
    content = re.sub(
        r'from app\.actions\.action_factory import ActionFactory',
        'from action_factory import ActionFactory',
        content
    )
    
    # Fix other app.actions imports
    content = re.sub(
        r'from app\.actions\.([a-zA-Z_]+) import',
        r'from \1 import',
        content
    )
    
    # Fix app.utils imports to use sys.path approach
    utils_imports = [
        'screenshot_manager', 'global_values_db', 'database', 'coordinate_validator',
        'image_matcher_ios', 'ios_device', 'appium_device_controller', 'directory_paths_db'
    ]
    
    for util_import in utils_imports:
        pattern = f'from app\\.utils\\.{util_import} import ([a-zA-Z_,\\s]+)'
        replacement = f'''import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from {util_import} import \\1'''
        
        content = re.sub(pattern, replacement, content)
    
    # Fix app.app imports
    app_imports = re.findall(r'from app\.app import ([a-zA-Z_,\s]+)', content)
    for imports in app_imports:
        pattern = f'from app\\.app import {re.escape(imports)}'
        replacement = f'''import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)
try:
    from app import {imports}
except ImportError:
    # Create dummy objects for missing imports
    {create_dummy_imports(imports)}'''
        
        content = re.sub(pattern, replacement, content)
    
    # Write back if changed
    if content != original_content:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"  ✅ Fixed imports in {os.path.basename(file_path)}")
        return True
    else:
        print(f"  ⏭️  No changes needed in {os.path.basename(file_path)}")
        return False

def create_dummy_imports(imports_str):
    """Create dummy import assignments for fallback"""
    imports = [imp.strip() for imp in imports_str.split(',')]
    dummy_lines = []
    
    for imp in imports:
        if 'current_' in imp:
            if 'counter' in imp or 'idx' in imp:
                dummy_lines.append(f"    {imp} = type('obj', (object,), {{'value': 0}})()")
            else:
                dummy_lines.append(f"    {imp} = None")
        else:
            dummy_lines.append(f"    {imp} = None")
    
    return '\n'.join(dummy_lines)

def main():
    """Main function to fix all action files"""
    # Get all Python files in the actions directory
    actions_dir = 'app/actions'
    action_files = glob.glob(os.path.join(actions_dir, '*.py'))
    
    # Exclude __init__.py and action_factory.py
    action_files = [f for f in action_files if not f.endswith('__init__.py') and not f.endswith('action_factory.py') and not f.endswith('test_action.py')]
    
    print(f"Found {len(action_files)} action files to process")
    
    fixed_count = 0
    for file_path in action_files:
        if fix_action_file_imports(file_path):
            fixed_count += 1
    
    print(f"\n✅ Fixed imports in {fixed_count} out of {len(action_files)} files")

if __name__ == '__main__':
    main()
