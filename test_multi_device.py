#!/usr/bin/env python3
"""
Test script to verify multi-device screenshot functionality.
Opens browsers with two different device IDs to test parallel usage.
"""

import sys
import webbrowser
import time
import os
import threading
import argparse
from urllib.parse import urlencode

def open_device_session(device_id, port=8080, delay=0):
    """Open a browser tab for a specific device"""
    if delay > 0:
        time.sleep(delay)
    
    # Create a unique session ID based on device ID and timestamp
    session_id = f"{device_id}_session_{int(time.time())}"
    
    # Create URL parameters
    params = {
        'deviceId': device_id,
        'platform': 'iOS',  # Default to iOS for testing
        'sessionId': session_id
    }
    
    # Format the URL with parameters
    url = f"http://localhost:{port}/?{urlencode(params)}"
    
    print(f"Opening browser for device: {device_id}")
    print(f"URL: {url}")
    
    # Open in the default browser
    webbrowser.open(url)
    
    return url

def open_multi_device_test(port=8080):
    """Open the multi-device test HTML page"""
    url = f"http://localhost:{port}/multi_device_test.html"
    print(f"Opening multi-device test page: {url}")
    webbrowser.open(url)
    return url

def main():
    parser = argparse.ArgumentParser(description='Test multi-device functionality')
    parser.add_argument('--port', type=int, default=8080, help='Port where the app is running')
    parser.add_argument('--mode', choices=['separate', 'html'], default='html', 
                        help='Test mode: separate (individual tabs) or html (test page)')
    parser.add_argument('--device1', default='00008030-00020C123E60402E', help='Device 1 ID')
    parser.add_argument('--device2', default='00008120-00186C801E13C01E', help='Device 2 ID')
    
    args = parser.parse_args()
    
    if args.mode == 'separate':
        # Open two separate browser tabs with different device IDs
        thread1 = threading.Thread(target=open_device_session, args=(args.device1, args.port, 0))
        thread2 = threading.Thread(target=open_device_session, args=(args.device2, args.port, 2))
        
        thread1.start()
        thread2.start()
        
        thread1.join()
        thread2.join()
    else:
        # Open the multi-device test HTML page
        open_multi_device_test(args.port)
    
    print("Test browser tabs opened. Please check that each shows the correct device.")
    print("Press Ctrl+C to exit.")
    
    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nTest terminated by user.")
        sys.exit(0)

if __name__ == "__main__":
    main() 