#!/bin/bash

# Multi-Device Mobile App Automation Tool Starter
# This script helps you start multiple instances of the automation tool
# for parallel device testing

echo "===== Multi-Device Mobile App Automation Tool Starter ====="
echo ""

# Function to display usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --help, -h          Show this help message"
    echo "  --instances N       Number of instances to start (default: 2)"
    echo "  --base-port PORT    Base port for Flask servers (default: 8080)"
    echo "  --base-appium PORT  Base port for Appium servers (default: 4723)"
    echo "  --base-wda PORT     Base port for WebDriverAgent (default: 8100)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Start 2 instances with default ports"
    echo "  $0 --instances 3                     # Start 3 instances"
    echo "  $0 --base-port 9000 --instances 2   # Start 2 instances with Flask on ports 9000, 9001"
    echo ""
    echo "Manual start examples:"
    echo "  python run.py --port 8081 --appium-port 4724 --wda-port 8101"
    echo "  python run.py --port 8082 --appium-port 4725 --wda-port 8102"
    exit 0
}

# Default values
INSTANCES=2
BASE_FLASK_PORT=8080
BASE_APPIUM_PORT=4723
BASE_WDA_PORT=8100

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_usage
            ;;
        --instances)
            INSTANCES="$2"
            shift 2
            ;;
        --base-port)
            BASE_FLASK_PORT="$2"
            shift 2
            ;;
        --base-appium)
            BASE_APPIUM_PORT="$2"
            shift 2
            ;;
        --base-wda)
            BASE_WDA_PORT="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            ;;
    esac
done

# Validate inputs
if ! [[ "$INSTANCES" =~ ^[0-9]+$ ]] || [ "$INSTANCES" -lt 1 ] || [ "$INSTANCES" -gt 10 ]; then
    echo "Error: Number of instances must be between 1 and 10"
    exit 1
fi

echo "Configuration:"
echo "  - Number of instances: $INSTANCES"
echo "  - Base Flask port: $BASE_FLASK_PORT"
echo "  - Base Appium port: $BASE_APPIUM_PORT"
echo "  - Base WDA port: $BASE_WDA_PORT"
echo ""

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Error: Virtual environment 'venv' not found."
    echo "Please create a virtual environment first:"
    echo "  python -m venv venv"
    echo "  source venv/bin/activate"
    echo "  pip install -r requirements.txt"
    exit 1
fi

# Function to start an instance
start_instance() {
    local instance_num=$1
    local flask_port=$((BASE_FLASK_PORT + instance_num))
    local appium_port=$((BASE_APPIUM_PORT + instance_num))
    local wda_port=$((BASE_WDA_PORT + instance_num))
    
    echo "Starting instance $((instance_num + 1)):"
    echo "  - Flask server: http://localhost:$flask_port"
    echo "  - Appium server: http://localhost:$appium_port"
    echo "  - WebDriverAgent: http://localhost:$wda_port"
    
    # Start in a new terminal window (macOS)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        osascript -e "tell application \"Terminal\" to do script \"cd '$PWD' && source venv/bin/activate && python run.py --port $flask_port --appium-port $appium_port --wda-port $wda_port\""
    # Start in a new terminal window (Linux with gnome-terminal)
    elif command -v gnome-terminal &> /dev/null; then
        gnome-terminal -- bash -c "cd '$PWD' && source venv/bin/activate && python run.py --port $flask_port --appium-port $appium_port --wda-port $wda_port; exec bash"
    # Start in a new terminal window (Linux with xterm)
    elif command -v xterm &> /dev/null; then
        xterm -e "cd '$PWD' && source venv/bin/activate && python run.py --port $flask_port --appium-port $appium_port --wda-port $wda_port; bash" &
    else
        echo "  Command to run manually:"
        echo "    source venv/bin/activate && python run.py --port $flask_port --appium-port $appium_port --wda-port $wda_port"
    fi
    
    echo ""
}

echo "Starting $INSTANCES instances..."
echo ""

# Start all instances
for ((i=0; i<INSTANCES; i++)); do
    start_instance $i
    sleep 2  # Small delay between starts
done

echo "All instances started!"
echo ""
echo "Access your automation tool instances at:"
for ((i=0; i<INSTANCES; i++)); do
    local flask_port=$((BASE_FLASK_PORT + i))
    echo "  Instance $((i + 1)): http://localhost:$flask_port"
done
echo ""
echo "Note: Each instance will use its own Appium server and WebDriverAgent port."
echo "You can connect different devices to different instances for parallel testing."
