#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix import statements in action files to use absolute imports
"""

import os
import re
import glob

def fix_action_file_imports(file_path):
    """Fix imports in a single action file to use absolute imports"""
    print(f"Fixing imports in {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Fix relative imports back to absolute imports
    content = re.sub(
        r'from \.base_action import BaseAction',
        'from app.actions.base_action import BaseAction',
        content
    )
    
    content = re.sub(
        r'from \.action_factory import ActionFactory',
        'from app.actions.action_factory import ActionFactory',
        content
    )
    
    # Fix other relative action imports
    content = re.sub(
        r'from \.([a-zA-Z_]+) import',
        r'from app.actions.\1 import',
        content
    )
    
    # Fix utils imports - change back to absolute imports
    content = re.sub(
        r'try:\s*\n\s*from \.\.utils\.([a-zA-Z_]+) import ([a-zA-Z_,\s]+)\s*\nexcept ImportError:.*?from utils\.\1 import \2',
        r'from app.utils.\1 import \2',
        content,
        flags=re.DOTALL
    )
    
    # Fix app imports - change back to absolute imports
    content = re.sub(
        r'try:\s*\n\s*import sys.*?from app import ([a-zA-Z_,\s]+)\s*\nexcept ImportError:.*?= None',
        r'from app.app import \1',
        content,
        flags=re.DOTALL
    )
    
    # Write back if changed
    if content != original_content:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"  ✅ Fixed imports in {os.path.basename(file_path)}")
        return True
    else:
        print(f"  ⏭️  No changes needed in {os.path.basename(file_path)}")
        return False

def main():
    """Main function to fix all action files"""
    # Get all Python files in the actions directory
    actions_dir = 'app/actions'
    action_files = glob.glob(os.path.join(actions_dir, '*.py'))
    
    # Exclude __init__.py and action_factory.py
    action_files = [f for f in action_files if not f.endswith('__init__.py') and not f.endswith('action_factory.py')]
    
    print(f"Found {len(action_files)} action files to process")
    
    fixed_count = 0
    for file_path in action_files:
        if fix_action_file_imports(file_path):
            fixed_count += 1
    
    print(f"\n✅ Fixed imports in {fixed_count} out of {len(action_files)} files")

if __name__ == '__main__':
    main()
