<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Device Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin: 20px 0;
        }
        .device-frame {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .device-header {
            padding: 10px;
            background-color: #4a6baf;
            color: white;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .device-controls {
            display: flex;
            gap: 10px;
        }
        .device-content {
            height: 800px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        button {
            background-color: #4a6baf;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #3a5a9f;
        }
        .device-id {
            font-family: monospace;
            font-size: 12px;
            background-color: rgba(0,0,0,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Multi-Device Test</h1>
    
    <div class="container">
        <div class="device-frame">
            <div class="device-header">
                <span>Device 1 <span class="device-id">00008030-00020C123E60402E</span></span>
                <div class="device-controls">
                    <button id="refresh1">Refresh</button>
                </div>
            </div>
            <div class="device-content">
                <iframe id="device1" src="http://localhost:8080/?deviceId=00008030-00020C123E60402E&platform=iOS&sessionId=unique1"></iframe>
            </div>
        </div>
        
        <div class="device-frame">
            <div class="device-header">
                <span>Device 2 <span class="device-id">00008120-00186C801E13C01E</span></span>
                <div class="device-controls">
                    <button id="refresh2">Refresh</button>
                </div>
            </div>
            <div class="device-content">
                <iframe id="device2" src="http://localhost:8080/?deviceId=00008120-00186C801E13C01E&platform=iOS&sessionId=unique2"></iframe>
            </div>
        </div>
    </div>
    
    <script>
        // Add refresh functionality
        document.getElementById('refresh1').addEventListener('click', function() {
            document.getElementById('device1').src = "http://localhost:8080/?deviceId=00008030-00020C123E60402E&platform=iOS&sessionId=unique1_" + Date.now();
        });
        
        document.getElementById('refresh2').addEventListener('click', function() {
            document.getElementById('device2').src = "http://localhost:8080/?deviceId=00008120-00186C801E13C01E&platform=iOS&sessionId=unique2_" + Date.now();
        });
    </script>
</body>
</html> 