#!/bin/bash
set -e

echo "===== Fixing Appium XCUITest Driver Issue ====="

# Check if Appium is installed
if ! command -v appium &> /dev/null; then
    echo "Appium not found. Installing Appium..."
    npm install -g appium@latest
fi

# Stop any running Appium instances
echo "Stopping any running Appium instances..."
pkill -f appium || true
sleep 2

# Get Appium driver location
APPIUM_HOME=$(npm list -g | head -1 | cut -d ' ' -f1)
echo "Appium home directory: $APPIUM_HOME"

# Update the XCUITest driver
echo "Updating XCUITest driver..."
appium driver update xcuitest

# Verify the XCUITest driver location
DRIVER_PATH=$(find $APPIUM_HOME -name "appium-xcuitest-driver" -type d 2>/dev/null | head -1)
if [ -z "$DRIVER_PATH" ]; then
    echo "❌ Could not find XCUITest driver path. Driver may not be installed properly."
    echo "Attempting to reinstall..."
    appium driver uninstall xcuitest || true
    appium driver install xcuitest
    DRIVER_PATH=$(find $APPIUM_HOME -name "appium-xcuitest-driver" -type d 2>/dev/null | head -1)
else
    echo "✅ Found XCUITest driver at: $DRIVER_PATH"
fi

# Create a configuration file for Appium to find drivers
echo "Creating Appium configuration file..."
CONFIG_DIR="$HOME/.appium"
mkdir -p $CONFIG_DIR

# Get Appium port from environment or use default
APPIUM_PORT=${APPIUM_PORT:-4723}

cat > $CONFIG_DIR/config.json << EOL
{
  "server": {
    "address": "127.0.0.1",
    "port": $APPIUM_PORT,
    "base-path": "/wd/hub",
    "relaxed-security": true
  },
  "driver": {
    "xcuitest": {
      "installPath": "$DRIVER_PATH"
    }
  }
}
EOL

echo "Created Appium config at: $CONFIG_DIR/config.json"

# Start Appium with proper configuration
echo "Starting Appium with proper configuration..."
appium --config $CONFIG_DIR/config.json > appium.log 2>&1 &
APPIUM_PID=$!
sleep 5

# Check if Appium is running
if ps -p $APPIUM_PID > /dev/null; then
    echo "✅ Appium started successfully with PID: $APPIUM_PID"
    echo "Log file: appium.log"
else
    echo "❌ Appium failed to start. Check appium.log for details:"
    tail -20 appium.log
    exit 1
fi

# Create a custom script to start Appium with the correct configuration
cat > start_appium.sh << EOL
#!/bin/bash
pkill -f appium || true
sleep 2
appium --config $CONFIG_DIR/config.json > appium.log 2>&1 &
echo "Appium started with proper configuration for XCUITest driver"
EOL

chmod +x start_appium.sh
echo "Created start_appium.sh script for future use"

echo ""
echo "===== Fix completed! ====="
echo "Try connecting to your iOS device again through the app."
echo "If you still encounter issues, check the logs in: appium.log"
echo "In the future, you can start Appium with the correct configuration using: ./start_appium.sh" 