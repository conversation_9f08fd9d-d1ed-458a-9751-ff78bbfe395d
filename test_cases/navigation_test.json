{"name": "Navigation Test", "description": "Test app navigation between screens", "created": "2025-06-13T09:28:30.000Z", "updated": "2025-06-13T09:28:30.000Z", "actions": [{"action_id": "al_NavStart", "type": "addLog", "message": "Starting navigation test", "level": "info"}, {"action_id": "tap_Menu", "type": "tap", "locator": "//XCUIElementTypeButton[@name='Menu']", "locator_type": "xpath", "description": "Tap menu button"}, {"action_id": "wait_Menu", "type": "wait", "duration": 1, "description": "Wait for menu to appear"}, {"action_id": "tap_Profile", "type": "tap", "locator": "//XCUIElementTypeButton[@name='Profile']", "locator_type": "xpath", "description": "Tap profile option"}, {"action_id": "wait_Profile", "type": "wait", "duration": 2, "description": "Wait for profile screen"}, {"action_id": "al_NavEnd", "type": "addLog", "message": "Navigation test completed", "level": "info"}]}