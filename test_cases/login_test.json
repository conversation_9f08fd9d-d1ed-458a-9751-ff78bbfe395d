{"name": "Login Test", "description": "Test user login functionality", "created": "2025-06-13T09:28:00.000Z", "updated": "2025-06-13T09:28:00.000Z", "actions": [{"action_id": "al_LoginStart", "type": "addLog", "message": "Starting login test", "level": "info"}, {"action_id": "tap_Username", "type": "tap", "locator": "//XCUIElementTypeTextField[@name='username']", "locator_type": "xpath", "description": "Tap on username field"}, {"action_id": "type_Username", "type": "input_text", "text": "<EMAIL>", "description": "Enter username"}, {"action_id": "tap_Password", "type": "tap", "locator": "//XCUIElementTypeSecureTextField[@name='password']", "locator_type": "xpath", "description": "Tap on password field"}, {"action_id": "type_Password", "type": "input_text", "text": "password123", "description": "Enter password"}, {"action_id": "tap_LoginBtn", "type": "tap", "locator": "//XCUIElementTypeButton[@name='Login']", "locator_type": "xpath", "description": "Tap login button"}, {"action_id": "al_LoginEnd", "type": "addLog", "message": "Login test completed", "level": "info"}]}