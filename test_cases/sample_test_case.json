{"name": "Sample Test Case", "description": "A sample test case to verify that test cases are preserved after app restart", "created": "2025-06-13T09:24:00.000Z", "updated": "2025-06-13T09:24:00.000Z", "actions": [{"action_id": "al_SampleLog1", "type": "addLog", "message": "Starting sample test case", "level": "info"}, {"action_id": "tap_Sample1", "type": "tap", "locator": "//XCUIElementTypeButton[@name='<PERSON>ting<PERSON>']", "locator_type": "xpath", "description": "Tap on Setting<PERSON> button"}, {"action_id": "wait_Sample1", "type": "wait", "duration": 2, "description": "Wait for 2 seconds"}, {"action_id": "al_SampleLog2", "type": "addLog", "message": "Sample test case completed successfully", "level": "info"}]}