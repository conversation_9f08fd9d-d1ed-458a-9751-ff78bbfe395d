#!/bin/bash

# Exit on error
set -e

echo "===== Restarting WebDriverAgent for iOS Devices ====="

# Kill any existing tidevice or WebDriverAgent processes
echo "Stopping any running WebDriverAgent processes..."
pkill -f tidevice || true
pkill -f WebDriverAgent || true
pkill -f iproxy || true
sleep 1

# Check and free ports
for PORT in 8100 8101 8102 8103; do
  if lsof -i :$PORT > /dev/null; then
    echo "Port $PORT is still in use, attempting to free it..."
    lsof -ti :$PORT | xargs kill -9 || true
    sleep 1
  fi
done

# Clear any existing port mappings
echo "Clearing previous port mappings..."
rm -f wda_ports.txt

# List connected iOS devices
echo "Checking for connected iOS devices..."
DEVICE_LIST=$(tidevice list)
echo "$DEVICE_LIST"

# Get array of device UDIDs - macOS compatible version
DEVICE_UDIDS=()
while read -r line; do
  # Skip header line
  if [[ ! $line =~ UDID ]]; then
    UDID=$(echo "$line" | awk '{print $1}')
    if [ -n "$UDID" ]; then
      DEVICE_UDIDS+=("$UDID")
    fi
  fi
done <<< "$(tidevice list)"

# If no devices found, try using idevice_id
if [ ${#DEVICE_UDIDS[@]} -eq 0 ]; then
  echo "No devices found with tidevice, trying idevice_id..."
  while read -r UDID; do
    if [ -n "$UDID" ]; then
      DEVICE_UDIDS+=("$UDID")
    fi
  done <<< "$(idevice_id -l)"
fi

echo "Found ${#DEVICE_UDIDS[@]} iOS device(s)"

# If no devices found
if [ ${#DEVICE_UDIDS[@]} -eq 0 ]; then
  echo "No iOS devices connected. Please connect an iOS device and try again."
  exit 1
fi

# Start WebDriverAgent for each device on different ports
PORT=8100
WDA_PIDS=()
for UDID in "${DEVICE_UDIDS[@]}"; do
  echo "Starting WebDriverAgent for device $UDID on port $PORT..."
  
  # Start WebDriverAgent in background
  tidevice -u $UDID wdaproxy -p $PORT > "wda_$UDID.log" 2>&1 &
  WDA_PID=$!
  
  # Remember the process ID
  WDA_PIDS+=($WDA_PID)
  
  # Save port mapping
  echo "$UDID:$PORT" >> wda_ports.txt
  
  echo "Waiting for WebDriverAgent to start on port $PORT..."
  sleep 5
  
  # Check if WebDriverAgent is running
  if curl -s http://localhost:$PORT/status > /dev/null; then
    echo "✅ WebDriverAgent started successfully for device $UDID on port $PORT"
  else
    echo "❌ WebDriverAgent failed to start for device $UDID. Check wda_$UDID.log for details."
    tail -10 "wda_$UDID.log"
    
    # Try one more time with a longer wait
    echo "Attempting to restart with a longer wait time..."
    kill $WDA_PID 2>/dev/null || true
    tidevice -u $UDID wdaproxy -p $PORT > "wda_$UDID.log" 2>&1 &
    WDA_PID=$!
    WDA_PIDS[-1]=$WDA_PID
    sleep 10
    
    if curl -s http://localhost:$PORT/status > /dev/null; then
      echo "✅ WebDriverAgent started successfully for device $UDID on port $PORT after retry"
    else
      echo "❌ WebDriverAgent still failed to start for device $UDID."
      tail -10 "wda_$UDID.log"
    fi
  fi
  
  # Increment port for next device
  PORT=$((PORT + 1))
done

# Verify all WDA instances are running
echo "Verifying all WebDriverAgent instances are running..."
while IFS=: read -r UDID PORT; do
  if curl -s http://localhost:$PORT/status > /dev/null; then
    echo "✅ WebDriverAgent is running for device $UDID on port $PORT"
  else
    echo "❌ WebDriverAgent is NOT running for device $UDID on port $PORT"
  fi
done < wda_ports.txt

echo "===== WebDriverAgent restart process completed ====="
echo "Device to port mappings:"
cat wda_ports.txt
echo ""
echo "To view logs for a specific device, use: tail -f wda_<UDID>.log" 