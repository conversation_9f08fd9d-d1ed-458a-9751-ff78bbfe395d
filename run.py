#!/usr/bin/env python
"""
Mobile App Automation Tool - Entry Point
"""
import os
import sys
import signal
import subprocess
import time
import logging
import argparse
import glob
import shutil
from pathlib import Path

# Add app directory to path
app_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app')
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

# Project root directory to path for config
root_dir = os.path.dirname(os.path.abspath(__file__))
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

# Import after adding app directory to path
sys.path.append(app_dir)
from app.app import app
from app.utils.appium_device_controller import AppiumDeviceController

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Set JAVA_HOME environment variable if not set correctly
try:
    # Check if JAVA_HOME is set and valid
    java_home = os.environ.get('JAVA_HOME')
    if not java_home or not os.path.exists(java_home):
        # Try to find Java home using /usr/libexec/java_home
        if sys.platform == 'darwin':  # macOS
            try:
                java_home_process = subprocess.run(
                    ['/usr/libexec/java_home'],
                    check=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                java_home = java_home_process.stdout.strip()
                if java_home and os.path.exists(java_home):
                    os.environ['JAVA_HOME'] = java_home
                    logger.info(f"Set JAVA_HOME to {java_home}")
                    print(f"Set JAVA_HOME to {java_home}")
            except Exception as e:
                logger.warning(f"Failed to set JAVA_HOME: {e}")
                print(f"Warning: Failed to set JAVA_HOME: {e}")
except Exception as e:
    logger.warning(f"Error checking/setting JAVA_HOME: {e}")
    print(f"Warning: Error checking/setting JAVA_HOME: {e}")

# Function to clean screenshots directory
def clean_screenshots_directory():
    """Delete all screenshots in the app/static/screenshots directory"""
    screenshots_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'static', 'screenshots')
    if os.path.exists(screenshots_dir):
        logger.info(f"Cleaning screenshots directory: {screenshots_dir}")
        try:
            # Delete all files in the directory
            files = glob.glob(os.path.join(screenshots_dir, '*'))
            for file in files:
                if os.path.isfile(file):
                    os.remove(file)
                    logger.debug(f"Deleted screenshot: {file}")
            logger.info(f"Cleaned {len(files)} screenshots")
        except Exception as e:
            logger.error(f"Error cleaning screenshots directory: {e}")
    else:
        logger.warning(f"Screenshots directory not found: {screenshots_dir}")
        # Create the directory if it doesn't exist
        try:
            os.makedirs(screenshots_dir, exist_ok=True)
            logger.info(f"Created screenshots directory: {screenshots_dir}")
        except Exception as e:
            logger.error(f"Error creating screenshots directory: {e}")

# Function to start Appium server
def start_appium_server(port=4723):
    """Start Appium server with inspector plugin and CORS enabled"""
    logger.info(f"Starting Appium server on port {port} with inspector plugin and CORS enabled...")
    try:
        # Build the command
        cmd = [
            'appium',
            '--port', str(port),
            '--use-plugins=inspector',
            '--allow-cors'
        ]

        # Start Appium as a background process
        appium_log = open('appium_server.log', 'w')
        process = subprocess.Popen(
            cmd,
            stdout=appium_log,
            stderr=appium_log,
            text=True
        )

        # Wait a bit to ensure Appium has started
        time.sleep(5)

        # Check if the process is still running
        if process.poll() is None:
            logger.info(f"Appium server started successfully on port {port}")
            return process
        else:
            logger.error(f"Appium server failed to start. Check appium_server.log for details.")
            return None
    except Exception as e:
        logger.error(f"Error starting Appium server: {e}")
        return None

# Function to start iproxy
def start_iproxy(device_port=8100, local_port=8100):
    """Start iproxy to forward iOS device port to local port"""
    logger.info(f"Starting iproxy to forward device port {device_port} to local port {local_port}...")
    try:
        # Build the command
        cmd = [
            'iproxy',
            str(local_port),
            str(device_port)
        ]

        # Start iproxy as a background process
        iproxy_log = open('iproxy.log', 'w')
        process = subprocess.Popen(
            cmd,
            stdout=iproxy_log,
            stderr=iproxy_log,
            text=True
        )

        # Wait a bit to ensure iproxy has started
        time.sleep(2)

        # Check if the process is still running
        if process.poll() is None:
            logger.info(f"iproxy started successfully (device port {device_port} -> local port {local_port})")
            return process
        else:
            logger.error(f"iproxy failed to start. Check iproxy.log for details.")
            return None
    except Exception as e:
        logger.error(f"Error starting iproxy: {e}")
        return None

# Function to kill existing processes
def kill_existing_processes():
    logger.info("Killing any existing Appium and iproxy processes...")
    try:
        if sys.platform == 'win32':
            # Windows
            subprocess.run(['taskkill', '/F', '/IM', 'node.exe'],
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
        else:
            # macOS/Linux
            subprocess.run(['pkill', '-f', 'appium'],
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
            subprocess.run(['pkill', '-f', 'iproxy'],
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
        time.sleep(2)  # Wait for processes to terminate
        logger.info("Existing processes terminated")
    except Exception as e:
        logger.error(f"Error killing processes: {e}")

# Signal handler for graceful shutdown
def signal_handler(sig, frame):
    print("\nShutting down and cleaning up...")
    if 'device_controller' in globals() and device_controller:
        device_controller.shutdown()
    sys.exit(0)

if __name__ == '__main__':
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Mobile App Automation Tool')
    parser.add_argument('--port', type=int, default=8080, help='Port to run the server on (default: 8080)')
    args = parser.parse_args()

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Kill any existing processes
    kill_existing_processes()

    # Initialize the device controller
    device_controller = AppiumDeviceController()
    # Make it available to the app
    import app.app as app_module
    app_module.device_controller = device_controller

    print(f"Starting Mobile App Automation Tool...")
    print(f"Open your web browser and navigate to: http://localhost:{args.port}")
    # Disable reloader to prevent state loss during development testing
    app.run(debug=True, use_reloader=False, host='0.0.0.0', port=args.port)