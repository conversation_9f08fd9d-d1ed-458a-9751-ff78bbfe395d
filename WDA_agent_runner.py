import subprocess
import argparse
import os
import sys
import shutil

def find_wda_path():
    """
    Finds the path to the WebDriverAgent.xcodeproj file.
    It checks common Appium installation directories.
    """
    print("🔍 Searching for WebDriverAgent.xcodeproj...")
    # Using 'mdfind' is a robust way to find the project on macOS
    try:
        # Search for the project directory, not the file itself, to get the correct path
        wda_project_dir = subprocess.check_output(
            ["mdfind", "kMDItemFSName == 'WebDriverAgent.xcodeproj'"],
            text=True
        ).strip().split('\n')[0]

        if wda_project_dir:
            path = os.path.dirname(wda_project_dir)
            print(f"✅ Found WebDriverAgent project at: {path}")
            return path
    except Exception:
        pass # mdfind might fail or return nothing, we'll fall back

    print("❌ Error: Could not automatically find the WebDriverAgent project via mdfind.")
    print("Please specify the path using the --wda-path argument.")
    return None

def check_prerequisites():
    """
    Checks if necessary command-line tools are installed.
    """
    print("🧐 Checking for prerequisites (xcodebuild)...")
    if not shutil.which("xcodebuild"):
        print("❌ Error: 'xcodebuild' not found. Please install Xcode and the Command Line Tools.")
        return False
    print("✅ Prerequisite is satisfied.")
    return True

def run_command(command, cwd):
    """
    Runs a shell command in a specified directory and prints its output in real-time.
    """
    print(f"🏃 Running command: {' '.join(command)}")
    process = subprocess.Popen(command, cwd=cwd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
    while True:
        output = process.stdout.readline()
        if output == '' and process.poll() is not None:
            break
        if output:
            print(output.strip())
    rc = process.poll()
    return rc

def main():
    """
    Main function to set up and run WebDriverAgent.
    """
    parser = argparse.ArgumentParser(
        description="A script to build and launch WebDriverAgent on a real iOS device.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("udid", help="The UDID of the target iOS device.")
    parser.add_argument("team_id", help="Your Apple Developer Team ID.")
    parser.add_argument("--wda-path", help="Optional: The path to the WebDriverAgent project directory.", default=None)
    parser.add_argument("--bundle-id", help="Optional: A custom bundle ID for WebDriverAgentRunner.", default="com.facebook.WebDriverAgentRunner.xctrunner")
    parser.add_argument("--wda-port", type=int, default=8100,
                        help="Optional: The port WebDriverAgent should listen on. Default is 8100.")

    args = parser.parse_args()

    if not check_prerequisites():
        sys.exit(1)

    wda_path = args.wda_path if args.wda_path else find_wda_path()
    if not wda_path:
        sys.exit(1)

    print(f"🚀 Starting WebDriverAgent on device {args.udid}...")
    print("This will install WebDriverAgentRunner on the device and start the server.")
    print("Xcode will resolve package dependencies automatically. This may take a few minutes on the first run.")
    print("Keep this script running to keep the agent active.")
    
    xcodebuild_command = [
        "xcodebuild",
        "-project", "WebDriverAgent.xcodeproj",
        "-scheme", "WebDriverAgentRunner",
        "-destination", f"id={args.udid}",
        "-derivedDataPath", "build",
        "test",
        f"DEVELOPMENT_TEAM_ID={args.team_id}",
        f"PRODUCT_BUNDLE_IDENTIFIER={args.bundle_id}",
        f"WebDriverAgentPort={args.wda_port}" # Pass the desired port to WDA
    ]

    try:
        if run_command(xcodebuild_command, cwd=wda_path) != 0:
            print("\n❌ WebDriverAgent setup failed.")
            print("Common issues to check:")
            print("  - Is the device unlocked and connected?")
            print("  - Is the Team ID correct?")
            print("  - Do you need to trust the developer in Settings > General > VPN & Device Management on the device?")
            print("  - Try a unique bundle ID with the --bundle-id flag if you have provisioning issues.")
        else:
            print(f"\n✅ WebDriverAgent is running and ready for automation on port {args.wda_port}!")
    except KeyboardInterrupt:
        print("\n🛑 WebDriverAgent stopped by user.")
        sys.exit(0)

if __name__ == "__main__":
    main()